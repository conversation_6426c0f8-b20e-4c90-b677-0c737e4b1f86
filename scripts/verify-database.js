#!/usr/bin/env node

/**
 * Database verification script for NarrAgent Supabase setup
 * This script verifies that the database schema and policies are correctly set up
 */

import { createClient } from '@supabase/supabase-js'
import { config } from 'dotenv'

// Load environment variables from parent directory
config({ path: '../.env' })

const supabaseUrl = process.env.SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function verifyTables() {
  console.log('🔍 Verifying database tables...')
  
  const tables = [
    'user_profiles',
    'organizations', 
    'organization_members',
    'agents',
    'chat_sessions',
    'chat_messages',
    'knowledge_bases',
    'documents',
    'usage_stats',
    'api_keys'
  ]
  
  for (const table of tables) {
    try {
      const { data, error } = await supabase
        .from(table)
        .select('*')
        .limit(1)
      
      if (error) {
        console.error(`❌ Table ${table}: ${error.message}`)
      } else {
        console.log(`✅ Table ${table}: OK`)
      }
    } catch (err) {
      console.error(`❌ Table ${table}: ${err.message}`)
    }
  }
}

async function verifyStorageBuckets() {
  console.log('\n🗂️  Verifying storage buckets...')

  const bucketsConfig = [
    { id: 'avatars', name: 'avatars', public: true },
    { id: 'chat-attachments', name: 'chat-attachments', public: false },
    { id: 'documents', name: 'documents', public: false },
    { id: 'exports', name: 'exports', public: false }
  ]

  try {
    const { data: allBuckets, error } = await supabase.storage.listBuckets()

    if (error) {
      console.error(`❌ Storage buckets: ${error.message}`)
      return
    }

    for (const bucketConfig of bucketsConfig) {
      const bucket = allBuckets.find(b => b.id === bucketConfig.id)
      if (bucket) {
        console.log(`✅ Bucket ${bucketConfig.id}: OK`)
      } else {
        console.log(`⚠️  Bucket ${bucketConfig.id}: Not found, creating...`)

        // Try to create the bucket
        const { data: newBucket, error: createError } = await supabase.storage.createBucket(
          bucketConfig.id,
          { public: bucketConfig.public }
        )

        if (createError) {
          console.error(`❌ Failed to create bucket ${bucketConfig.id}: ${createError.message}`)
        } else {
          console.log(`✅ Bucket ${bucketConfig.id}: Created successfully`)
        }
      }
    }
  } catch (err) {
    console.error(`❌ Storage buckets: ${err.message}`)
  }
}

async function insertSeedData() {
  console.log('\n🌱 Inserting seed data...')
  
  try {
    // Insert default organization
    const { data: org, error: orgError } = await supabase
      .from('organizations')
      .upsert({
        id: '00000000-0000-0000-0000-000000000001',
        name: 'NarrAgent Demo',
        slug: 'narragent-demo',
        description: 'Default organization for NarrAgent demo'
      })
      .select()
    
    if (orgError) {
      console.error(`❌ Organization seed: ${orgError.message}`)
    } else {
      console.log('✅ Organization seed: OK')
    }
    
    // Insert default agents
    const agents = [
      {
        id: '00000000-0000-0000-0000-000000000001',
        organization_id: '00000000-0000-0000-0000-000000000001',
        name: 'General Assistant',
        description: 'A helpful AI assistant for general questions and tasks',
        model_config: { model: 'gpt-4', temperature: 0.7, max_tokens: 2000 },
        system_prompt: 'You are a helpful AI assistant. Provide clear, accurate, and helpful responses to user questions.',
        tools: [],
        is_public: true
      },
      {
        id: '00000000-0000-0000-0000-000000000002',
        organization_id: '00000000-0000-0000-0000-000000000001',
        name: 'Web Search Agent',
        description: 'An AI agent that can search the web for current information',
        model_config: { model: 'gpt-4', temperature: 0.3, max_tokens: 2000 },
        system_prompt: 'You are a web search assistant. Use the search tools to find current information and provide accurate, up-to-date responses.',
        tools: [{ name: 'web_search', description: 'Search the web for information' }],
        is_public: true
      }
    ]
    
    const { data: agentData, error: agentError } = await supabase
      .from('agents')
      .upsert(agents)
      .select()
    
    if (agentError) {
      console.error(`❌ Agents seed: ${agentError.message}`)
    } else {
      console.log('✅ Agents seed: OK')
    }
    
    // Insert default knowledge base
    const { data: kb, error: kbError } = await supabase
      .from('knowledge_bases')
      .upsert({
        id: '00000000-0000-0000-0000-000000000001',
        organization_id: '00000000-0000-0000-0000-000000000001',
        name: 'NarrAgent Documentation',
        description: 'Official documentation and guides for NarrAgent'
      })
      .select()
    
    if (kbError) {
      console.error(`❌ Knowledge base seed: ${kbError.message}`)
    } else {
      console.log('✅ Knowledge base seed: OK')
    }
    
  } catch (err) {
    console.error(`❌ Seed data: ${err.message}`)
  }
}

async function verifyRLS() {
  console.log('\n🔒 Verifying Row Level Security...')
  
  // This is a basic check - in a real scenario you'd test with different user contexts
  try {
    const { data, error } = await supabase.rpc('has_organization_role', {
      org_id: '00000000-0000-0000-0000-000000000001',
      user_id: '00000000-0000-0000-0000-000000000001',
      required_role: 'member'
    })
    
    console.log('✅ RLS functions: OK')
  } catch (err) {
    console.log('⚠️  RLS functions: Could not verify (this is normal for service role)')
  }
}

async function main() {
  console.log('🚀 Starting NarrAgent database verification...\n')
  
  await verifyTables()
  await verifyStorageBuckets()
  await insertSeedData()
  await verifyRLS()
  
  console.log('\n✨ Database verification completed!')
  console.log('\n📊 Next steps:')
  console.log('1. Visit your Supabase dashboard to explore the data')
  console.log('2. Test the authentication flow in your application')
  console.log('3. Start integrating the frontend with Supabase')
}

main().catch(console.error)
