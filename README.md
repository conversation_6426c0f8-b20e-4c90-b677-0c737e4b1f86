# NarrAgent

NarrAgent 是基于 Agno 项目模板搭建的智能代理平台，包含前端 Agent UI 和后端 Agent API。本项目正在改造为基于 Supabase 的 SaaS 服务平台。

## 📋 项目概述

本项目是一个现代化的 AI 代理平台，提供：
- 🤖 **智能代理交互界面**：现代化的聊天界面，支持实时流式响应
- 🔧 **工具调用可视化**：展示代理工具调用过程和结果
- 🧠 **推理步骤展示**：显示代理的思考过程
- 📚 **引用来源支持**：显示代理使用的信息来源
- 🎨 **多模态支持**：处理文本、图像、视频、音频等多种内容类型
- 🔐 **会话管理**：支持会话历史记录和状态持久化

## 🏗️ 项目结构

```
agno/
├── agent-ui/          # 前端应用 (Next.js)
├── agent-api/         # 后端 API (FastAPI)
├── docs/              # 项目文档
└── README.md          # 项目说明
```

## 🚀 技术栈

### 前端技术栈 (Agent UI)

#### 核心框架
- **Next.js 15.2.3** - React 全栈框架，支持 SSR/SSG
- **React 19.0.0** - 用户界面库
- **TypeScript 5.x** - 类型安全的 JavaScript

#### UI 组件与样式
- **Tailwind CSS 3.4.1** - 原子化 CSS 框架
- **shadcn/ui** - 基于 Radix UI 的组件库
  - `@radix-ui/react-dialog` - 对话框组件
  - `@radix-ui/react-select` - 选择器组件
  - `@radix-ui/react-tooltip` - 提示框组件
  - `@radix-ui/react-icons` - 图标库
- **Lucide React** - 现代图标库
- **Framer Motion 12.4.1** - 动画库
- **class-variance-authority** - 样式变体管理
- **clsx & tailwind-merge** - 条件样式合并

#### 状态管理与数据处理
- **Zustand 5.0.3** - 轻量级状态管理
- **nuqs 2.3.2** - URL 状态同步
- **next-themes 0.4.4** - 主题管理

#### 内容处理
- **react-markdown 9.0.3** - Markdown 渲染
- **rehype-raw & rehype-sanitize** - HTML 处理和安全
- **remark-gfm** - GitHub 风格 Markdown
- **dayjs 1.11.13** - 日期处理

#### 用户体验
- **sonner 1.7.4** - 通知组件
- **use-stick-to-bottom** - 聊天滚动优化

#### 开发工具
- **ESLint 9.x** - 代码检查
- **Prettier 3.4.2** - 代码格式化
- **PostCSS 8.x** - CSS 后处理

### 后端技术栈 (Agent API)

#### 核心框架
- **Python 3.11+** - 编程语言
- **FastAPI** - 现代异步 Web 框架
- **Uvicorn** - ASGI 服务器
- **Pydantic** - 数据验证和序列化

#### AI 与代理
- **Agno 1.4.6** - 核心代理框架
- **OpenAI 1.78.0** - GPT 模型集成

#### 数据库与存储
- **PostgreSQL** - 主数据库
- **pgvector 0.4.1** - 向量数据库扩展
- **SQLAlchemy 2.0.40** - ORM 框架
- **psycopg 3.2.7** - PostgreSQL 适配器

#### 工具与集成
- **duckduckgo-search** - 网络搜索工具
- **yfinance** - 金融数据工具
- **requests & httpx** - HTTP 客户端

#### 开发工具
- **uv** - Python 包管理器
- **Ruff** - 代码检查和格式化
- **MyPy** - 类型检查
- **Docker & Docker Compose** - 容器化部署

## 🎯 Supabase 集成计划

### 当前状态
- ✅ 使用 Zustand + localStorage 进行前端状态管理
- ✅ 使用 PostgreSQL + SQLAlchemy 进行后端数据存储
- ✅ 基本的会话管理和历史记录功能

### 计划集成的 Supabase 功能

#### 1. 认证系统 (Authentication)
- **用户注册/登录**：邮箱、社交登录 (Google, GitHub)
- **用户管理**：用户资料、权限控制
- **会话管理**：JWT token 管理
- **多租户支持**：组织和团队管理

#### 2. 数据库 (Database)
- **用户数据**：用户资料、偏好设置
- **代理会话**：聊天历史、会话状态
- **知识库**：文档、向量嵌入
- **使用统计**：API 调用、使用量追踪

#### 3. 实时功能 (Realtime)
- **实时聊天**：WebSocket 连接
- **协作功能**：多用户共享会话
- **状态同步**：跨设备状态同步

#### 4. 存储 (Storage)
- **文件上传**：图片、文档、音频
- **媒体处理**：自动压缩、格式转换
- **CDN 分发**：全球内容分发

#### 5. Edge Functions
- **API 代理**：安全的第三方 API 调用
- **数据处理**：实时数据转换
- **Webhook 处理**：外部服务集成

### 技术实现路径

#### 前端改造
1. **安装 Supabase 客户端**
   ```bash
   pnpm add @supabase/supabase-js @supabase/auth-ui-react @supabase/auth-ui-shared
   ```

2. **认证集成**
   - 替换当前的端点选择为用户认证
   - 添加登录/注册页面
   - 实现受保护的路由

3. **数据层重构**
   - 将 Zustand store 与 Supabase 实时数据同步
   - 实现离线优先的数据缓存策略
   - 添加数据同步状态指示器

4. **实时功能**
   - 使用 Supabase Realtime 替换当前的 HTTP 轮询
   - 实现实时聊天和状态同步

#### 后端改造
1. **认证中间件**
   - 集成 Supabase JWT 验证
   - 实现用户上下文传递
   - 添加权限控制

2. **数据库迁移**
   - 设计 Supabase 数据库 schema
   - 实现数据迁移脚本
   - 配置 Row Level Security (RLS)

3. **API 重构**
   - 添加用户隔离
   - 实现多租户数据访问
   - 优化查询性能

## 🛠️ 开发环境设置

### 前端开发 (Agent UI)

```bash
cd agent-ui

# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 代码检查
pnpm lint

# 格式化代码
pnpm format:fix

# 类型检查
pnpm typecheck
```

### 后端开发 (Agent API)

```bash
cd agent-api

# 配置代理（可选）
export http_proxy="http://127.0.0.1:7890"
export https_proxy="http://127.0.0.1:7890"

# 创建虚拟环境
uv venv -p 3.11

# 激活虚拟环境
source .venv/bin/activate

# 安装依赖
uv pip install -r requirements.txt

# 配置环境变量
export OPENAI_API_KEY="your-api-key"

# 启动服务
docker compose up -d
```

## 📚 API 文档

- **前端应用**: http://localhost:3000
- **后端 API**: http://localhost:8000
- **API 文档**: http://localhost:8000/docs
- **数据库**: localhost:5432

## 🔧 配置说明

### 环境变量

#### Agent API
```bash
# OpenAI API
OPENAI_API_KEY=your-openai-api-key

# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_USER=ai
DB_PASS=ai
DB_DATABASE=ai

# Agno 监控 (可选)
AGNO_MONITOR=True
AGNO_API_KEY=your-agno-api-key
```

#### Agent UI
```bash
# API 端点
NEXT_PUBLIC_API_URL=http://localhost:8000

# Supabase 配置 (计划中)
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
```

## 🚀 部署指南

### Docker 部署

```bash
# 构建并启动所有服务
docker compose up -d --build

# 查看日志
docker compose logs -f

# 停止服务
docker compose down
```

### 生产环境部署

1. **构建生产镜像**
   ```bash
   ./scripts/build_image.sh
   ```

2. **部署到云平台**
   - Google Cloud Run
   - AWS App Runner
   - Azure Container Apps
   - Railway.app
   - Render

## 📈 路线图

### Phase 1: Supabase 基础集成 (4-6 周)
- [ ] Supabase 项目设置和配置
- [ ] 用户认证系统实现
- [ ] 数据库 schema 设计和迁移
- [ ] 基础的用户管理功能

### Phase 2: 核心功能迁移 (6-8 周)
- [ ] 会话数据迁移到 Supabase
- [ ] 实时聊天功能实现
- [ ] 文件上传和存储集成
- [ ] 用户偏好和设置管理

### Phase 3: 高级功能 (8-10 周)
- [ ] 多租户和团队功能
- [ ] 高级权限控制
- [ ] 使用量统计和计费
- [ ] 第三方集成和 Webhook

### Phase 4: 优化和扩展 (持续)
- [ ] 性能优化
- [ ] 监控和日志
- [ ] 移动端适配
- [ ] 国际化支持

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 支持

- 📚 [Agno 文档](https://docs.agno.com)
- 💬 [Discord 社区](https://agno.link/discord)
- ❓ [社区论坛](https://agno.link/community)
- 🐛 [问题反馈](https://github.com/agno-agi/agent-api/issues)