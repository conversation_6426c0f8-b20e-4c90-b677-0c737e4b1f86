# Supabase 集成技术方案

## 概述

本文档详细说明如何将当前基于 Agno 的 Agent UI 和 Agent API 改造为基于 Supabase 的 SaaS 服务平台。

## 当前架构分析

### 前端 (Agent UI)
- **状态管理**: Zustand + localStorage
- **数据获取**: 直接 HTTP API 调用
- **会话管理**: URL 状态 + 本地存储
- **实时通信**: HTTP 轮询

### 后端 (Agent API)
- **数据库**: PostgreSQL + SQLAlchemy
- **认证**: 无（开放 API）
- **会话存储**: PostgresAgentStorage
- **API**: FastAPI RESTful

## Supabase 集成架构

### 1. 认证系统重构

#### 1.1 前端认证集成

**安装依赖**
```bash
pnpm add @supabase/supabase-js @supabase/auth-ui-react @supabase/auth-ui-shared
```

**Supabase 客户端配置**
```typescript
// lib/supabase.ts
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)
```

**认证状态管理**
```typescript
// hooks/useAuth.ts
import { useEffect, useState } from 'react'
import { User } from '@supabase/supabase-js'
import { supabase } from '@/lib/supabase'

export function useAuth() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // 获取当前用户
    supabase.auth.getUser().then(({ data: { user } }) => {
      setUser(user)
      setLoading(false)
    })

    // 监听认证状态变化
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        setUser(session?.user ?? null)
        setLoading(false)
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  return { user, loading }
}
```

#### 1.2 后端认证中间件

**JWT 验证中间件**
```python
# api/middleware/auth.py
from fastapi import HTTPException, Depends
from fastapi.security import HTTPBearer
import jwt
from supabase import create_client, Client

security = HTTPBearer()

def get_supabase_client() -> Client:
    url = os.environ.get("SUPABASE_URL")
    key = os.environ.get("SUPABASE_SERVICE_ROLE_KEY")
    return create_client(url, key)

async def get_current_user(token: str = Depends(security)):
    try:
        # 验证 Supabase JWT
        supabase = get_supabase_client()
        user = supabase.auth.get_user(token.credentials)
        return user
    except Exception:
        raise HTTPException(status_code=401, detail="Invalid token")
```

### 2. 数据库架构设计

#### 2.1 核心表结构

**用户相关表**
```sql
-- 用户资料扩展表
CREATE TABLE user_profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  display_name TEXT,
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 组织表
CREATE TABLE organizations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 组织成员表
CREATE TABLE organization_members (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  role TEXT NOT NULL DEFAULT 'member',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(organization_id, user_id)
);
```

**代理和会话表**
```sql
-- 代理配置表
CREATE TABLE agents (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  model_config JSONB NOT NULL,
  system_prompt TEXT,
  tools JSONB DEFAULT '[]',
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 会话表
CREATE TABLE chat_sessions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  agent_id UUID REFERENCES agents(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  title TEXT,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 消息表
CREATE TABLE chat_messages (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  session_id UUID REFERENCES chat_sessions(id) ON DELETE CASCADE,
  role TEXT NOT NULL, -- 'user', 'assistant', 'system'
  content TEXT NOT NULL,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 2.2 Row Level Security (RLS) 策略

```sql
-- 启用 RLS
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE organization_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE agents ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_messages ENABLE ROW LEVEL SECURITY;

-- 用户只能访问自己的资料
CREATE POLICY "Users can view own profile" ON user_profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON user_profiles
  FOR UPDATE USING (auth.uid() = id);

-- 组织成员可以访问组织数据
CREATE POLICY "Organization members can view agents" ON agents
  FOR SELECT USING (
    organization_id IN (
      SELECT organization_id FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );

-- 用户只能访问自己的会话
CREATE POLICY "Users can view own sessions" ON chat_sessions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own sessions" ON chat_sessions
  FOR INSERT WITH CHECK (auth.uid() = user_id);
```

### 3. 实时功能实现

#### 3.1 前端实时订阅

```typescript
// hooks/useRealtimeChat.ts
import { useEffect, useState } from 'react'
import { supabase } from '@/lib/supabase'
import { ChatMessage } from '@/types'

export function useRealtimeChat(sessionId: string) {
  const [messages, setMessages] = useState<ChatMessage[]>([])

  useEffect(() => {
    // 订阅新消息
    const subscription = supabase
      .channel(`chat_session_${sessionId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'chat_messages',
          filter: `session_id=eq.${sessionId}`
        },
        (payload) => {
          setMessages(prev => [...prev, payload.new as ChatMessage])
        }
      )
      .subscribe()

    return () => {
      subscription.unsubscribe()
    }
  }, [sessionId])

  return { messages, setMessages }
}
```

#### 3.2 后端实时推送

```python
# api/services/realtime.py
from supabase import create_client

class RealtimeService:
    def __init__(self):
        self.supabase = create_client(
            os.environ.get("SUPABASE_URL"),
            os.environ.get("SUPABASE_SERVICE_ROLE_KEY")
        )
    
    async def broadcast_message(self, session_id: str, message: dict):
        """广播消息到指定会话"""
        await self.supabase.realtime.send(
            channel=f"chat_session_{session_id}",
            event="new_message",
            payload=message
        )
    
    async def broadcast_typing(self, session_id: str, user_id: str, is_typing: bool):
        """广播打字状态"""
        await self.supabase.realtime.send(
            channel=f"chat_session_{session_id}",
            event="typing",
            payload={"user_id": user_id, "is_typing": is_typing}
        )
```

### 4. 文件存储集成

#### 4.1 前端文件上传

```typescript
// hooks/useFileUpload.ts
import { supabase } from '@/lib/supabase'

export function useFileUpload() {
  const uploadFile = async (file: File, bucket: string = 'chat-attachments') => {
    const fileExt = file.name.split('.').pop()
    const fileName = `${Date.now()}.${fileExt}`
    const filePath = `${bucket}/${fileName}`

    const { data, error } = await supabase.storage
      .from(bucket)
      .upload(filePath, file)

    if (error) throw error

    // 获取公共 URL
    const { data: { publicUrl } } = supabase.storage
      .from(bucket)
      .getPublicUrl(filePath)

    return { path: filePath, url: publicUrl }
  }

  return { uploadFile }
}
```

#### 4.2 存储桶配置

```sql
-- 创建存储桶
INSERT INTO storage.buckets (id, name, public) 
VALUES ('chat-attachments', 'chat-attachments', true);

-- 设置存储策略
CREATE POLICY "Users can upload own files" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'chat-attachments' AND 
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can view own files" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'chat-attachments' AND 
    auth.uid()::text = (storage.foldername(name))[1]
  );
```

### 5. Edge Functions 集成

#### 5.1 AI 代理调用函数

```typescript
// supabase/functions/chat-completion/index.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

serve(async (req) => {
  try {
    const { message, sessionId, agentId } = await req.json()
    
    // 验证用户权限
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )
    
    // 调用 OpenAI API
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${Deno.env.get('OPENAI_API_KEY')}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4',
        messages: [{ role: 'user', content: message }],
        stream: true
      })
    })
    
    // 流式返回结果
    return new Response(response.body, {
      headers: { 'Content-Type': 'text/plain' }
    })
    
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    })
  }
})
```

### 6. 迁移策略

#### 6.1 数据迁移脚本

```python
# scripts/migrate_to_supabase.py
import asyncio
from sqlalchemy import create_engine
from supabase import create_client

async def migrate_sessions():
    """迁移现有会话数据到 Supabase"""
    # 连接现有数据库
    old_engine = create_engine("postgresql://...")
    
    # 连接 Supabase
    supabase = create_client(
        os.environ.get("SUPABASE_URL"),
        os.environ.get("SUPABASE_SERVICE_ROLE_KEY")
    )
    
    # 迁移逻辑
    # 1. 读取现有会话数据
    # 2. 转换数据格式
    # 3. 插入到 Supabase
    pass

if __name__ == "__main__":
    asyncio.run(migrate_sessions())
```

#### 6.2 渐进式迁移

1. **Phase 1**: 并行运行两套系统
2. **Phase 2**: 新功能只在 Supabase 实现
3. **Phase 3**: 逐步迁移现有功能
4. **Phase 4**: 完全切换到 Supabase

## 实施时间表

### Week 1-2: 基础设置
- [ ] Supabase 项目创建和配置
- [ ] 数据库 schema 设计
- [ ] 基础认证系统实现

### Week 3-4: 核心功能
- [ ] 会话管理迁移
- [ ] 实时聊天实现
- [ ] 文件上传集成

### Week 5-6: 高级功能
- [ ] 多租户支持
- [ ] 权限控制
- [ ] Edge Functions 部署

### Week 7-8: 测试和优化
- [ ] 性能测试
- [ ] 安全审计
- [ ] 用户体验优化

## 风险评估

### 技术风险
- **数据迁移复杂性**: 中等
- **实时功能稳定性**: 中等
- **性能影响**: 低

### 业务风险
- **用户体验中断**: 低（渐进式迁移）
- **数据丢失**: 低（备份策略）
- **成本增加**: 中等（Supabase 定价）

## 成本分析

### Supabase 定价
- **免费层**: 500MB 数据库，1GB 存储，2GB 带宽
- **Pro 层**: $25/月，8GB 数据库，100GB 存储，250GB 带宽
- **Team 层**: $599/月，无限制

### 预估成本
- **开发阶段**: 免费层
- **生产环境**: Pro 层起步
- **企业级**: Team 层

## 总结

通过集成 Supabase，我们可以：
1. 快速实现用户认证和多租户功能
2. 获得实时数据同步能力
3. 简化文件存储和 CDN 分发
4. 利用 Edge Functions 优化性能
5. 降低基础设施维护成本

这个方案将显著提升产品的 SaaS 化程度和用户体验。
