# NarrAgent 测试指南

## 🚀 快速开始

### 1. 启动应用
```bash
cd agent-ui
npm run dev
```

访问: http://localhost:3000

### 2. 测试认证流程

#### 📝 用户注册
1. 访问 http://localhost:3000
2. 应用会自动重定向到登录页面 `/auth`
3. 点击"立即注册"切换到注册模式
4. 填写信息：
   - 显示名称：测试用户
   - 邮箱：<EMAIL>
   - 密码：123456（至少6位）
   - 确认密码：123456
5. 点击"创建账户"
6. 检查邮箱验证邮件（Supabase 会发送）

#### 🔑 用户登录
1. 在登录页面填写：
   - 邮箱：<EMAIL>
   - 密码：123456
2. 点击"登录"
3. 成功后应重定向到主页面

#### 🌐 社交登录
1. 点击"使用 Google 继续"或"使用 GitHub 继续"
2. 完成 OAuth 流程
3. 自动重定向回应用

### 3. 测试主界面功能

#### 👤 用户菜单
1. 登录后，查看左侧边栏底部的用户菜单
2. 显示用户头像（首字母或上传的图片）
3. 显示用户名和邮箱
4. 点击用户菜单展开选项：
   - 个人资料（暂时显示提示）
   - 设置（暂时显示提示）
   - 退出登录

#### 🤖 代理选择
1. 在侧边栏中查看"Agent"部分
2. 应该显示从 Supabase 获取的公开代理：
   - General Assistant
   - Web Search Agent
   - Code Assistant
3. 选择一个代理会创建新的会话

#### 💬 会话管理
1. 选择代理后，会在"会话历史"部分看到新会话
2. 会话功能：
   - 点击会话切换
   - 悬停显示编辑和删除按钮
   - 双击或点击编辑按钮修改标题
   - 点击删除按钮删除会话

### 4. 测试数据持久化

#### 🔄 会话恢复
1. 创建几个会话
2. 刷新页面
3. 检查会话是否保持
4. 检查用户登录状态是否保持

#### 📱 多设备同步
1. 在另一个浏览器标签页登录同一账户
2. 创建新会话
3. 检查是否在其他标签页实时同步

### 5. 测试错误处理

#### ❌ 认证错误
1. 尝试使用错误密码登录
2. 尝试注册已存在的邮箱
3. 检查错误提示是否正确显示

#### 🔒 权限测试
1. 退出登录
2. 尝试直接访问 http://localhost:3000
3. 应该自动重定向到登录页面

### 6. 测试响应式设计

#### 📱 移动端
1. 打开浏览器开发者工具
2. 切换到移动设备视图
3. 测试登录页面和主界面的响应式布局

## 🐛 常见问题排查

### 问题1：无法连接到 Supabase
**症状**: 登录时显示网络错误
**解决**: 
1. 检查 `.env.local` 文件中的 Supabase 配置
2. 确认 Supabase 项目状态正常
3. 检查网络连接

### 问题2：代理列表为空
**症状**: 侧边栏显示"暂无可用代理"
**解决**:
1. 检查 Supabase 数据库中是否有种子数据
2. 运行数据库验证脚本：
   ```bash
   cd scripts
   npm run verify
   ```

### 问题3：会话创建失败
**症状**: 选择代理后没有创建会话
**解决**:
1. 检查浏览器控制台错误
2. 确认用户已正确登录
3. 检查 Supabase RLS 策略

### 问题4：实时同步不工作
**症状**: 多个标签页之间数据不同步
**解决**:
1. 检查 Supabase Realtime 是否启用
2. 确认订阅设置正确
3. 检查网络连接稳定性

## ✅ 测试检查清单

### 基础功能
- [ ] 应用启动无错误
- [ ] 自动重定向到登录页面
- [ ] 用户注册功能正常
- [ ] 用户登录功能正常
- [ ] 社交登录功能正常
- [ ] 用户菜单显示正确
- [ ] 退出登录功能正常

### 数据功能
- [ ] 代理列表正确加载
- [ ] 会话创建功能正常
- [ ] 会话列表显示正确
- [ ] 会话切换功能正常
- [ ] 会话编辑功能正常
- [ ] 会话删除功能正常

### 用户体验
- [ ] 页面加载速度合理
- [ ] 动画效果流畅
- [ ] 错误提示清晰
- [ ] 响应式设计正常
- [ ] 中文本地化完整

### 数据持久化
- [ ] 刷新页面后状态保持
- [ ] 多标签页数据同步
- [ ] 用户资料正确保存
- [ ] 会话历史正确保存

## 📊 性能测试

### 加载性能
1. 打开浏览器开发者工具
2. 切换到 Network 标签
3. 刷新页面，检查：
   - 首次加载时间 < 3秒
   - 资源加载无失败
   - 图片优化良好

### 内存使用
1. 切换到 Performance 标签
2. 录制一段使用过程
3. 检查内存使用是否合理

## 🎯 下一步测试

完成基础测试后，可以进行：

1. **集成测试**: 连接后端 API，测试完整的聊天流程
2. **压力测试**: 创建大量会话和消息，测试性能
3. **安全测试**: 测试 RLS 策略和权限控制
4. **兼容性测试**: 在不同浏览器和设备上测试

## 📝 测试报告

测试完成后，请记录：
- 发现的问题和解决方案
- 性能数据
- 用户体验反馈
- 改进建议

---

**祝测试顺利！** 🎉
