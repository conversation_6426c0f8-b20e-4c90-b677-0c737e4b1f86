# FFmpeg 重复加载错误修复

## 🐛 问题描述

在上传视频文件时，控制台出现以下错误：

```
Error: FFmpeg is already loading
    at FFmpegService.load (webpack-internal:///(app-pages-browser)/./src/lib/ffmpeg.ts:47:19)
    at useFFmpeg.useCallback[load] [as load] (webpack-internal:///(app-pages-browser)/./src/hooks/useFFmpeg.ts:83:78)
    at NarrationWorkspace.useCallback[loadFFmpeg] (webpack-internal:///(app-pages-browser)/./src/components/narration/NarrationWorkspace.tsx:68:30)
```

## 🔍 问题原因

这个错误是由于 FFmpeg 加载过程中的竞态条件导致的：

1. **多重触发**：用户上传文件时，多个地方同时尝试加载 FFmpeg
   - `useEffect` 检测到文件变化自动加载
   - `handleFilesUploaded` 中的自动处理逻辑也尝试加载
   - 用户手动点击处理按钮时再次尝试加载

2. **状态不同步**：不同组件和 hook 之间的加载状态没有正确同步

3. **错误处理不当**：原来的实现在检测到重复加载时直接抛出错误，而不是等待加载完成

## 🔧 修复方案

### 1. 优化 useFFmpeg Hook

修改 `src/hooks/useFFmpeg.ts` 中的 `load` 方法：

```typescript
const load = useCallback(async (useMultithread = true) => {
  // 如果已经加载完成，直接返回
  if (loadState.isLoaded) {
    return
  }

  // 如果正在加载，等待加载完成而不是抛出错误
  if (loadState.isLoading) {
    addLog('FFmpeg 正在加载中，等待加载完成...')
    
    // 轮询等待加载完成
    return new Promise<void>((resolve, reject) => {
      const checkLoadState = () => {
        if (loadState.isLoaded) {
          resolve()
        } else if (loadState.error) {
          reject(new Error(loadState.error))
        } else if (loadState.isLoading) {
          // 继续等待
          setTimeout(checkLoadState, 100)
        } else {
          // 加载状态异常，重新尝试加载
          load(useMultithread).then(resolve).catch(reject)
        }
      }
      checkLoadState()
    })
  }

  // 正常的加载逻辑...
}, [loadState, onProgress, onLog, addLog])
```

### 2. 优化 FFmpegService 类

修改 `src/lib/ffmpeg.ts` 中的 `load` 方法：

```typescript
async load(
  useMultithread = true,
  onProgress?: (progress: number, time: number) => void,
  onLog?: (message: string) => void
): Promise<void> {
  if (this.isLoaded) {
    return
  }

  if (this.isLoading) {
    // 如果正在加载，等待加载完成而不是抛出错误
    return new Promise<void>((resolve, reject) => {
      const checkLoadState = () => {
        if (this.isLoaded) {
          resolve()
        } else if (this.isLoading) {
          // 继续等待
          setTimeout(checkLoadState, 100)
        } else {
          // 加载失败，拒绝 Promise
          reject(new Error('FFmpeg loading failed'))
        }
      }
      checkLoadState()
    })
  }

  // 正常的加载逻辑...
}
```

### 3. 优化 NarrationWorkspace 组件

修改 `src/components/narration/NarrationWorkspace.tsx`：

```typescript
// 加载 FFmpeg
const loadFFmpeg = useCallback(async () => {
  // 如果已经加载完成，直接返回
  if (ffmpeg.loadState.isLoaded) {
    return
  }

  // 如果正在加载，等待加载完成
  if (ffmpeg.loadState.isLoading) {
    return
  }

  // 正常的加载逻辑...
}, [ffmpeg])

// 处理文件上传
const handleFilesUploaded = useCallback((uploadedFiles: VideoFileInfo[]) => {
  setFiles(uploadedFiles)
  setAudioResult(null)

  // 单文件模式自动开始处理
  if (!modeConfig.supportMultipleFiles && uploadedFiles.length === 1) {
    // 延迟执行，确保状态更新完成
    setTimeout(async () => {
      try {
        // 确保 FFmpeg 已加载
        if (!ffmpeg.loadState.isLoaded) {
          await loadFFmpeg()
        }
        
        // 开始提取音频
        await extractAudio(uploadedFiles[0])
      } catch (error) {
        console.error('自动处理失败:', error)
        toast.error('自动处理失败，请手动重试')
      }
    }, 1000)
  }
}, [modeConfig, ffmpeg.loadState, extractAudio, loadFFmpeg])
```

## 📋 修复要点

1. **等待而不是抛出错误**：当检测到 FFmpeg 正在加载时，等待加载完成而不是抛出错误
2. **状态检查优化**：在每次调用加载方法前，先检查当前的加载状态
3. **Promise 链式处理**：使用 Promise 来正确处理异步加载过程
4. **错误处理改进**：提供更好的错误恢复机制

## 🎯 预期效果

- ✅ **消除重复加载错误**：不再出现 "FFmpeg is already loading" 错误
- ✅ **提升用户体验**：用户可以正常上传和处理视频文件
- ✅ **状态同步**：不同组件之间的 FFmpeg 加载状态保持同步
- ✅ **错误恢复**：当加载失败时，提供重试机制

## 🔍 验证方法

1. 上传视频文件
2. 检查控制台是否还有 FFmpeg 加载错误
3. 验证视频处理功能是否正常工作
4. 测试多次快速上传文件的场景

## 📝 注意事项

- 修复后的代码使用轮询方式等待加载完成，间隔为 100ms
- 如果加载失败，会正确抛出错误并允许重试
- 保持了原有的功能和 API 接口不变
