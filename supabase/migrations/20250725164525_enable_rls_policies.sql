-- Enable Row Level Security on all tables
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE organization_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE agents ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE knowledge_bases ENABLE ROW LEVEL SECURITY;
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE usage_stats ENABLE ROW LEVEL SECURITY;
ALTER TABLE api_keys ENABLE ROW LEVEL SECURITY;

-- Helper function to check if user is organization member
CREATE OR REPLACE FUNCTION is_organization_member(org_id UUID, user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM organization_members
    WHERE organization_id = org_id AND organization_members.user_id = user_id
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to check if user has specific role in organization
CREATE OR REPLACE FUNCTION has_organization_role(org_id UUID, user_id UUID, required_role user_role)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM organization_members
    WHERE organization_id = org_id
    AND organization_members.user_id = user_id
    AND role = required_role
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to check if user is admin or owner in organization
CREATE OR REPLACE FUNCTION is_organization_admin(org_id UUID, user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM organization_members
    WHERE organization_id = org_id
    AND organization_members.user_id = user_id
    AND role IN ('admin', 'owner')
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- User profiles policies
CREATE POLICY "Users can view own profile" ON user_profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON user_profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON user_profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

-- Organizations policies
CREATE POLICY "Organization members can view organization" ON organizations
  FOR SELECT USING (
    is_organization_member(id, auth.uid())
  );

CREATE POLICY "Organization admins can update organization" ON organizations
  FOR UPDATE USING (
    is_organization_admin(id, auth.uid())
  );

CREATE POLICY "Authenticated users can create organizations" ON organizations
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Organization members policies
CREATE POLICY "Organization members can view members" ON organization_members
  FOR SELECT USING (
    is_organization_member(organization_id, auth.uid())
  );

CREATE POLICY "Organization admins can manage members" ON organization_members
  FOR ALL USING (
    is_organization_admin(organization_id, auth.uid())
  );

CREATE POLICY "Users can join organizations when invited" ON organization_members
  FOR INSERT WITH CHECK (
    auth.uid() = user_id OR
    is_organization_admin(organization_id, auth.uid())
  );

-- Agents policies
CREATE POLICY "Organization members can view agents" ON agents
  FOR SELECT USING (
    is_organization_member(organization_id, auth.uid()) OR is_public = true
  );

CREATE POLICY "Organization admins can manage agents" ON agents
  FOR ALL USING (
    is_organization_admin(organization_id, auth.uid())
  );

CREATE POLICY "Organization members can create agents" ON agents
  FOR INSERT WITH CHECK (
    is_organization_member(organization_id, auth.uid()) AND
    auth.uid() = created_by
  );

-- Chat sessions policies
CREATE POLICY "Users can view own sessions" ON chat_sessions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own sessions" ON chat_sessions
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own sessions" ON chat_sessions
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own sessions" ON chat_sessions
  FOR DELETE USING (auth.uid() = user_id);

-- Chat messages policies
CREATE POLICY "Users can view messages from own sessions" ON chat_messages
  FOR SELECT USING (
    session_id IN (
      SELECT id FROM chat_sessions WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert messages to own sessions" ON chat_messages
  FOR INSERT WITH CHECK (
    session_id IN (
      SELECT id FROM chat_sessions WHERE user_id = auth.uid()
    )
  );

-- Knowledge bases policies
CREATE POLICY "Organization members can view knowledge bases" ON knowledge_bases
  FOR SELECT USING (
    is_organization_member(organization_id, auth.uid())
  );

CREATE POLICY "Organization admins can manage knowledge bases" ON knowledge_bases
  FOR ALL USING (
    is_organization_admin(organization_id, auth.uid())
  );

CREATE POLICY "Organization members can create knowledge bases" ON knowledge_bases
  FOR INSERT WITH CHECK (
    is_organization_member(organization_id, auth.uid()) AND
    auth.uid() = created_by
  );

-- Documents policies
CREATE POLICY "Organization members can view documents" ON documents
  FOR SELECT USING (
    knowledge_base_id IN (
      SELECT id FROM knowledge_bases
      WHERE is_organization_member(organization_id, auth.uid())
    )
  );

CREATE POLICY "Organization members can manage documents" ON documents
  FOR ALL USING (
    knowledge_base_id IN (
      SELECT id FROM knowledge_bases
      WHERE is_organization_member(organization_id, auth.uid())
    )
  );

-- Usage stats policies
CREATE POLICY "Organization members can view usage stats" ON usage_stats
  FOR SELECT USING (
    is_organization_member(organization_id, auth.uid())
  );

CREATE POLICY "System can insert usage stats" ON usage_stats
  FOR INSERT WITH CHECK (true); -- Allow system to insert usage stats

-- API keys policies
CREATE POLICY "Organization admins can view API keys" ON api_keys
  FOR SELECT USING (
    is_organization_admin(organization_id, auth.uid())
  );

CREATE POLICY "Organization admins can manage API keys" ON api_keys
  FOR ALL USING (
    is_organization_admin(organization_id, auth.uid())
  );