-- Create storage buckets if they don't exist
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES
  ('avatars', 'avatars', true, 5242880, ARRAY['image/jpeg', 'image/png', 'image/webp']),
  ('chat-attachments', 'chat-attachments', false, 52428800, NULL),
  ('documents', 'documents', false, 104857600, NULL),
  ('exports', 'exports', false, 1073741824, ARRAY['application/json', 'text/csv', 'application/pdf'])
ON CONFLICT (id) DO NOTHING;

-- Add a function to allow system operations (for seed data)
CREATE OR REPLACE FUNCTION is_system_operation()
RETURNS BOOLEAN AS $$
BEGIN
  -- Check if the current role is service_role (used for system operations)
  RETURN auth.role() = 'service_role';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update organizations policies to allow system operations
DROP POLICY IF EXISTS "Authenticated users can create organizations" ON organizations;
CREATE POLICY "System and authenticated users can create organizations" ON organizations
  FOR INSERT WITH CHECK (
    auth.role() = 'authenticated' OR is_system_operation()
  );

-- Update agents policies to allow system operations
DROP POLICY IF EXISTS "Organization members can create agents" ON agents;
CREATE POLICY "Organization members and system can create agents" ON agents
  FOR INSERT WITH CHECK (
    (is_organization_member(organization_id, auth.uid()) AND auth.uid() = created_by) OR
    is_system_operation()
  );

-- Update knowledge bases policies to allow system operations
DROP POLICY IF EXISTS "Organization members can create knowledge bases" ON knowledge_bases;
CREATE POLICY "Organization members and system can create knowledge bases" ON knowledge_bases
  FOR INSERT WITH CHECK (
    (is_organization_member(organization_id, auth.uid()) AND auth.uid() = created_by) OR
    is_system_operation()
  );