-- Seed data for development and testing

-- Insert a default organization (for development)
INSERT INTO organizations (id, name, slug, description) VALUES 
  ('00000000-0000-0000-0000-000000000001', 'NarrAgent Demo', 'narragent-demo', 'Default organization for NarrAgent demo');

-- Insert default agents (these will be available to all organizations)
INSERT INTO agents (id, organization_id, name, description, model_config, system_prompt, tools, is_public, created_by) VALUES 
  (
    '00000000-0000-0000-0000-000000000001',
    '00000000-0000-0000-0000-000000000001',
    'General Assistant',
    'A helpful AI assistant for general questions and tasks',
    '{"model": "gpt-4", "temperature": 0.7, "max_tokens": 2000}',
    'You are a helpful AI assistant. Provide clear, accurate, and helpful responses to user questions.',
    '[]',
    true,
    NULL
  ),
  (
    '00000000-0000-0000-0000-000000000002',
    '00000000-0000-0000-0000-000000000001',
    'Web Search Agent',
    'An AI agent that can search the web for current information',
    '{"model": "gpt-4", "temperature": 0.3, "max_tokens": 2000}',
    'You are a web search assistant. Use the search tools to find current information and provide accurate, up-to-date responses.',
    '[{"name": "web_search", "description": "Search the web for information"}]',
    true,
    NULL
  ),
  (
    '00000000-0000-0000-0000-000000000003',
    '00000000-0000-0000-0000-000000000001',
    'Code Assistant',
    'An AI assistant specialized in programming and code-related tasks',
    '{"model": "gpt-4", "temperature": 0.2, "max_tokens": 3000}',
    'You are a programming assistant. Help users with coding questions, debugging, code review, and software development best practices.',
    '[]',
    true,
    NULL
  );

-- Insert a default knowledge base
INSERT INTO knowledge_bases (id, organization_id, name, description, created_by) VALUES 
  (
    '00000000-0000-0000-0000-000000000001',
    '00000000-0000-0000-0000-000000000001',
    'NarrAgent Documentation',
    'Official documentation and guides for NarrAgent',
    NULL
  );

-- Insert some sample documents
INSERT INTO documents (knowledge_base_id, title, content, created_by) VALUES 
  (
    '00000000-0000-0000-0000-000000000001',
    'Getting Started with NarrAgent',
    'NarrAgent is an intelligent agent platform that allows you to create and deploy AI agents for various tasks. This guide will help you get started with creating your first agent.',
    NULL
  ),
  (
    '00000000-0000-0000-0000-000000000001',
    'Agent Configuration Guide',
    'Learn how to configure your AI agents with different models, prompts, and tools. This guide covers best practices for agent setup and optimization.',
    NULL
  );
