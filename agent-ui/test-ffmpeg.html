<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FFmpeg 加载测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .log {
            background: #f5f5f5;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.loading {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .status.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <h1>FFmpeg 加载测试</h1>
    
    <div>
        <button id="loadBtn" onclick="testFFmpegLoad()">测试 FFmpeg 加载</button>
        <button id="clearBtn" onclick="clearLog()">清空日志</button>
    </div>
    
    <div id="status" class="status">等待测试</div>
    
    <div class="log" id="log"></div>

    <script type="module">
        import { FFmpeg } from 'https://unpkg.com/@ffmpeg/ffmpeg@0.12.10/dist/esm/index.js';
        import { fetchFile, toBlobURL } from 'https://unpkg.com/@ffmpeg/util@0.12.1/dist/esm/index.js';

        window.FFmpeg = FFmpeg;
        window.fetchFile = fetchFile;
        window.toBlobURL = toBlobURL;

        let ffmpeg = null;
        let logs = [];

        function addLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            logs.push(logEntry);
            
            const logElement = document.getElementById('log');
            logElement.innerHTML = logs.join('\n');
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(logEntry);
        }

        function setStatus(message, type = 'loading') {
            const statusElement = document.getElementById('status');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }

        async function testFFmpegLoad() {
            const loadBtn = document.getElementById('loadBtn');
            loadBtn.disabled = true;
            
            try {
                setStatus('开始测试 FFmpeg 加载...', 'loading');
                addLog('=== 开始 FFmpeg 加载测试 ===');
                
                // 创建 FFmpeg 实例
                ffmpeg = new FFmpeg();
                addLog('FFmpeg 实例创建成功');
                
                // 设置事件监听器
                ffmpeg.on('log', ({ message }) => {
                    addLog(`FFmpeg: ${message}`);
                });
                
                ffmpeg.on('progress', ({ progress, time }) => {
                    addLog(`Progress: ${(progress * 100).toFixed(1)}% (${(time / 1000000).toFixed(2)}s)`);
                });
                
                // 首先尝试多线程版本
                let loadSuccess = false;
                
                try {
                    addLog('尝试加载多线程版本...');
                    await loadFFmpegWithConfig(true);
                    loadSuccess = true;
                    addLog('多线程版本加载成功');
                } catch (error) {
                    addLog(`多线程版本加载失败: ${error.message}`);
                    addLog('尝试单线程版本...');
                    
                    try {
                        await loadFFmpegWithConfig(false);
                        loadSuccess = true;
                        addLog('单线程版本加载成功');
                    } catch (error2) {
                        addLog(`单线程版本加载失败: ${error2.message}`);
                        throw error2;
                    }
                }
                
                if (loadSuccess) {
                    // 验证 FFmpeg 是否可用
                    addLog('验证 FFmpeg 实例...');
                    await ffmpeg.exec(['-version']);
                    addLog('FFmpeg 验证成功');
                    
                    setStatus('FFmpeg 加载并验证成功！', 'success');
                    addLog('=== FFmpeg 测试完成 ===');
                } else {
                    throw new Error('FFmpeg 加载失败');
                }
                
            } catch (error) {
                addLog(`测试失败: ${error.message}`);
                setStatus(`测试失败: ${error.message}`, 'error');
            } finally {
                loadBtn.disabled = false;
            }
        }

        async function loadFFmpegWithConfig(useMultithread) {
            const corePackage = useMultithread ? '@ffmpeg/core-mt' : '@ffmpeg/core';
            const baseURL = `https://unpkg.com/${corePackage}@0.12.6/dist/umd`;

            const loadConfig = {
                coreURL: await toBlobURL(`${baseURL}/ffmpeg-core.js`, 'text/javascript'),
                wasmURL: await toBlobURL(`${baseURL}/ffmpeg-core.wasm`, 'application/wasm'),
            };

            if (useMultithread) {
                loadConfig.workerURL = await toBlobURL(
                    `${baseURL}/ffmpeg-core.worker.js`,
                    'text/javascript'
                );
            }

            await ffmpeg.load(loadConfig);
        }

        function clearLog() {
            logs = [];
            document.getElementById('log').innerHTML = '';
            setStatus('日志已清空', 'loading');
        }

        // 全局函数
        window.testFFmpegLoad = testFFmpegLoad;
        window.clearLog = clearLog;
    </script>
</body>
</html>
