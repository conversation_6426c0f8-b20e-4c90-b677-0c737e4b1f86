import type { NextConfig } from 'next'

const nextConfig: NextConfig = {
  devIndicators: false,

  // 减少 hydration 不匹配的严重性（主要用于处理浏览器扩展添加的属性）
  experimental: {
    // 在开发环境中减少 hydration 错误的严重性
    ...(process.env.NODE_ENV === 'development' && {
      // 这个选项可以帮助减少由浏览器扩展引起的 hydration 警告
      optimizePackageImports: ['lucide-react', '@radix-ui/react-icons']
    })
  },

  // 配置 headers 以支持 SharedArrayBuffer (FFmpeg 多线程需要)
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Cross-Origin-Embedder-Policy',
            value: 'require-corp',
          },
          {
            key: 'Cross-Origin-Opener-Policy',
            value: 'same-origin',
          },
        ],
      },
    ]
  },

  // 配置 webpack 以支持 WebAssembly
  webpack: (config, { isServer }) => {
    // 支持 WebAssembly
    config.experiments = {
      ...config.experiments,
      asyncWebAssembly: true,
    }

    // 支持 .wasm 文件
    config.module.rules.push({
      test: /\.wasm$/,
      type: 'webassembly/async',
    })

    // 在客户端环境中排除 Node.js 特定的模块
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        crypto: false,
      }
    }

    return config
  },
}

export default nextConfig
