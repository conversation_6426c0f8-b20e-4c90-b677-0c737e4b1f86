# FFmpeg.wasm 集成文档

## 概述

本项目已成功集成 FFmpeg.wasm，为 NarrAgent 平台提供强大的浏览器端媒体处理能力。用户可以在不上传文件到服务器的情况下，直接在浏览器中处理视频和音频文件。

## 功能特性

### 🎥 视频处理
- **格式转换**: 支持 MP4, WebM, AVI, MOV, MKV 等格式互转
- **视频压缩**: 可调整质量、分辨率、比特率
- **视频剪辑**: 支持按时间段剪切视频
- **添加水印**: 支持文字水印添加
- **视频分割**: 将长视频分割为多个片段

### 🎵 音频处理
- **格式转换**: 支持 MP3, WAV, AAC, OGG, FLAC 等格式
- **音频提取**: 从视频文件中提取音频
- **音频压缩**: 调整比特率和采样率
- **音频剪辑**: 按时间段剪切音频

### 🔒 隐私安全
- **本地处理**: 所有处理完全在浏览器中进行
- **无服务器上传**: 文件不会离开用户设备
- **自动清理**: 处理完成后自动清理临时文件
- **开源技术**: 基于开源的 FFmpeg.wasm

## 使用方式

### 1. 在现有多媒体组件中使用

当查看视频或音频内容时，会看到"处理"按钮：

```typescript
// 视频组件中的处理按钮
<MediaProcessButton
  mediaUrl={videoUrl}
  mediaType="video"
  filename="video.mp4"
/>

// 音频组件中的处理按钮
<MediaProcessButton
  mediaUrl={audioUrl}
  mediaType="audio"
  filename="audio.mp3"
/>
```

### 2. 独立媒体处理页面

访问 `/media` 路径可以使用完整的媒体处理工具：

- 文件上传（拖拽或点击选择）
- 预设配置选择
- 自定义处理选项
- 实时进度显示
- 处理结果下载

### 3. 编程接口

#### 使用 FFmpeg Hook

```typescript
import { useFFmpeg } from '@/hooks/useFFmpeg'

function MyComponent() {
  const ffmpeg = useFFmpeg()
  
  // 加载 FFmpeg
  await ffmpeg.load()
  
  // 转换视频
  const result = await ffmpeg.convertVideo(file, 'mp4', {
    quality: 'high',
    resolution: '1920x1080'
  })
  
  // 下载结果
  ffmpeg.downloadResult(result, 'output.mp4')
}
```

#### 使用 FFmpeg 服务

```typescript
import { ffmpegService, ffmpegOperations } from '@/lib/ffmpeg'

// 加载 FFmpeg
await ffmpegService.load()

// 转换视频
const result = await ffmpegOperations.convertVideo(file, 'mp4', {
  quality: 'high'
})

// 提取音频
const audio = await ffmpegOperations.extractAudio(videoFile, 'mp3')
```

## 技术架构

### 核心组件

1. **FFmpeg 服务层** (`src/lib/ffmpeg.ts`)
   - 封装 FFmpeg.wasm 核心功能
   - 提供文件操作和命令执行接口
   - 管理 FFmpeg 实例生命周期

2. **React Hook** (`src/hooks/useFFmpeg.ts`)
   - 提供 React 友好的 FFmpeg 接口
   - 管理加载状态和处理进度
   - 支持批量处理

3. **UI 组件**
   - `MediaProcessor`: 主要的媒体处理界面
   - `MediaProcessButton`: 集成到现有组件的处理按钮
   - `ProcessingProgress`: 进度显示组件

4. **类型定义** (`src/types/media.ts`)
   - 完整的 TypeScript 类型支持
   - 预设配置定义
   - 工具函数

### 配置

#### Next.js 配置 (`next.config.ts`)

```typescript
// 支持 SharedArrayBuffer (多线程 FFmpeg)
async headers() {
  return [{
    source: '/(.*)',
    headers: [
      { key: 'Cross-Origin-Embedder-Policy', value: 'require-corp' },
      { key: 'Cross-Origin-Opener-Policy', value: 'same-origin' }
    ]
  }]
}

// 支持 WebAssembly
webpack: (config) => {
  config.experiments = { ...config.experiments, asyncWebAssembly: true }
  return config
}
```

## 预设配置

系统提供了常用的预设配置：

- **视频转 MP4 (高清)**: 转换为 MP4 格式，保持高清质量
- **视频压缩 (中等)**: 平衡质量和文件大小的压缩
- **音频转 MP3**: 转换为 MP3 格式
- **提取音频**: 从视频中提取音频并保存为 MP3

## 性能优化

### 多线程支持
- 默认使用多线程版本的 FFmpeg.wasm
- 需要 SharedArrayBuffer 支持
- 显著提升处理速度

### 内存管理
- 自动清理临时文件
- 优化大文件处理
- 进度反馈和错误处理

### 用户体验
- 实时进度显示
- 拖拽文件上传
- 快速操作按钮
- 处理结果预览

## 浏览器兼容性

### 支持的浏览器
- Chrome 88+
- Firefox 79+
- Safari 14+
- Edge 88+

### 要求
- WebAssembly 支持
- SharedArrayBuffer 支持（多线程版本）
- 现代 JavaScript 特性

## 故障排除

### 常见问题

1. **FFmpeg 加载失败**
   - 检查网络连接
   - 确认浏览器支持 WebAssembly
   - 尝试使用单线程版本

2. **SharedArrayBuffer 错误**
   - 确认 HTTPS 环境
   - 检查 COOP/COEP 头部设置
   - 降级到单线程版本

3. **内存不足**
   - 处理较小的文件
   - 关闭其他标签页
   - 使用较低的质量设置

### 调试

启用详细日志：

```typescript
const ffmpeg = useFFmpeg()

// 查看处理日志
console.log(ffmpeg.logs)

// 监听进度
ffmpeg.progress // 包含进度信息
```

## 扩展开发

### 添加新的处理类型

1. 在 `types/media.ts` 中定义新类型
2. 在 `lib/ffmpeg.ts` 中实现处理逻辑
3. 在 `hooks/useFFmpeg.ts` 中添加便捷方法
4. 更新 UI 组件支持新功能

### 自定义预设

```typescript
const customPreset: ProcessingPreset = {
  id: 'custom-preset',
  name: '自定义预设',
  description: '自定义处理配置',
  type: 'convert',
  mediaType: 'video',
  options: {
    format: 'mp4',
    quality: 'high',
    resolution: '1280x720'
  }
}
```

## 更新日志

### v1.0.0 (当前版本)
- ✅ 基础 FFmpeg.wasm 集成
- ✅ 视频和音频格式转换
- ✅ 视频剪辑功能
- ✅ 音频提取功能
- ✅ 实时进度显示
- ✅ 多线程支持
- ✅ UI 组件集成

### 计划功能
- 🔄 批量处理
- 🔄 更多视频滤镜
- 🔄 音频混合
- 🔄 字幕处理
- 🔄 视频合并

## 贡献

欢迎提交 Issue 和 Pull Request 来改进 FFmpeg.wasm 集成功能。

## 许可证

本集成基于 FFmpeg.wasm 的 LGPL 许可证。
