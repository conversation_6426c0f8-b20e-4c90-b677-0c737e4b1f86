import type { Metadata } from 'next'
import { D<PERSON>_Mono, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'next/font/google'
import { NuqsAdapter } from 'nuqs/adapters/next/app'
import { Toaster } from '@/components/ui/sonner'
import { AuthProvider } from '@/components/auth/AuthProvider'
import { HydrationFix } from '@/components/HydrationFix'
import './globals.css'
const geistSans = Geist({
  variable: '--font-geist-sans',
  weight: '400',
  subsets: ['latin']
})

const dmMono = DM_Mono({
  subsets: ['latin'],
  variable: '--font-dm-mono',
  weight: '400'
})

const poppins = Poppins({
  subsets: ['latin'],
  variable: '--font-poppins',
  weight: ['400', '500', '600']
})

export const metadata: Metadata = {
  title: 'NarrAgent',
  description:
    'NarrAgent 智能代理平台 - 基于 Next.js、Tailwind CSS 和 TypeScript 构建的现代化 AI 代理交互界面。',
  icons: {
    icon: [
      { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
      { url: '/favicon.ico', sizes: 'any' }
    ],
    apple: [
      { url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' }
    ],
    other: [
      { rel: 'android-chrome-192x192', url: '/android-chrome-192x192.png', sizes: '192x192' },
      { rel: 'android-chrome-512x512', url: '/android-chrome-512x512.png', sizes: '512x512' }
    ]
  },
  manifest: '/site.webmanifest'
}

export default function RootLayout({
  children
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="zh-CN" suppressHydrationWarning>
      <body className={`${geistSans.variable} ${dmMono.variable} ${poppins.variable} antialiased`}>
        <HydrationFix />
        <AuthProvider>
          <NuqsAdapter>{children}</NuqsAdapter>
          <Toaster
            theme="dark"
            position="bottom-right"
            toastOptions={{
              style: {
                background: '#27272A',
                color: '#FAFAFA',
                border: '1px solid rgba(255, 255, 255, 0.2)',
              },
              className: 'toast-custom',
            }}
          />
        </AuthProvider>
      </body>
    </html>
  )
}
