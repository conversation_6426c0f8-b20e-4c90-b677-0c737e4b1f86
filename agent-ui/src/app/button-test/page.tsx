'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import Icon from '@/components/ui/icon'

export default function ButtonTestPage() {
  return (
    <div className="container mx-auto p-8">
      <Card className="max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>按钮样式测试</CardTitle>
        </CardHeader>
        <CardContent className="space-y-8">
          
          {/* 默认按钮 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">默认按钮 (default)</h3>
            <div className="flex gap-4 flex-wrap">
              <Button>
                <Icon type="download" size="xs" />
                下载音频文件
              </Button>
              <Button disabled>
                <Icon type="download" size="xs" />
                下载音频文件 (禁用)
              </Button>
            </div>
          </div>

          {/* Outline 按钮 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">轮廓按钮 (outline)</h3>
            <div className="flex gap-4 flex-wrap">
              <Button variant="outline">
                <Icon type="refresh" size="xs" />
                重新开始
              </Button>
              <Button variant="outline" disabled>
                <Icon type="refresh" size="xs" />
                重新开始 (禁用)
              </Button>
            </div>
          </div>

          {/* Secondary 按钮 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">次要按钮 (secondary)</h3>
            <div className="flex gap-4 flex-wrap">
              <Button variant="secondary">
                <Icon type="settings" size="xs" />
                设置
              </Button>
              <Button variant="secondary" disabled>
                <Icon type="settings" size="xs" />
                设置 (禁用)
              </Button>
            </div>
          </div>

          {/* Ghost 按钮 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">幽灵按钮 (ghost)</h3>
            <div className="flex gap-4 flex-wrap">
              <Button variant="ghost">
                <Icon type="play" size="xs" />
                播放
              </Button>
              <Button variant="ghost" disabled>
                <Icon type="play" size="xs" />
                播放 (禁用)
              </Button>
            </div>
          </div>

          {/* Destructive 按钮 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">危险按钮 (destructive)</h3>
            <div className="flex gap-4 flex-wrap">
              <Button variant="destructive">
                <Icon type="trash" size="xs" />
                删除
              </Button>
              <Button variant="destructive" disabled>
                <Icon type="trash" size="xs" />
                删除 (禁用)
              </Button>
            </div>
          </div>

          {/* 不同尺寸 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">不同尺寸</h3>
            <div className="flex gap-4 items-center flex-wrap">
              <Button size="sm">小按钮</Button>
              <Button size="default">默认按钮</Button>
              <Button size="lg">大按钮</Button>
            </div>
          </div>

          {/* 模拟音频提取完成的按钮组 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">音频提取完成按钮组 (实际使用场景)</h3>
            <div className="flex gap-3">
              <Button
                className="flex items-center gap-2"
                style={{
                  backgroundColor: '#4f25b8',
                  color: '#ffffff',
                  border: 'none'
                }}
              >
                <Icon type="download" size="xs" />
                下载音频文件
              </Button>

              <Button
                variant="outline"
                style={{
                  borderColor: '#4f25b8',
                  color: '#ffffff',
                  backgroundColor: 'transparent'
                }}
              >
                <Icon type="refresh" size="xs" className="mr-2" />
                重新开始
              </Button>
            </div>
          </div>

          {/* 对比测试 - 原始样式 vs 修复样式 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">对比测试</h3>
            <div className="space-y-4">
              <div>
                <p className="text-sm text-gray-400 mb-2">原始样式（可能看不清）：</p>
                <div className="flex gap-3">
                  <Button className="flex items-center gap-2">
                    <Icon type="download" size="xs" />
                    下载音频文件
                  </Button>
                  <Button variant="outline">
                    <Icon type="refresh" size="xs" className="mr-2" />
                    重新开始
                  </Button>
                </div>
              </div>

              <div>
                <p className="text-sm text-gray-400 mb-2">修复后样式（应该清晰可见）：</p>
                <div className="flex gap-3">
                  <Button
                    className="flex items-center gap-2"
                    style={{
                      backgroundColor: '#4f25b8',
                      color: '#ffffff',
                      border: 'none'
                    }}
                  >
                    <Icon type="download" size="xs" />
                    下载音频文件
                  </Button>

                  <Button
                    variant="outline"
                    style={{
                      borderColor: '#4f25b8',
                      color: '#ffffff',
                      backgroundColor: 'transparent'
                    }}
                  >
                    <Icon type="refresh" size="xs" className="mr-2" />
                    重新开始
                  </Button>
                </div>
              </div>
            </div>
          </div>

        </CardContent>
      </Card>
    </div>
  )
}
