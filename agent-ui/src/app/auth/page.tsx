'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { AuthForm } from '@/components/auth/AuthForm'
import { useAuthContext } from '@/components/auth/AuthProvider'
import Heading from '@/components/ui/typography/Heading'
import Paragraph from '@/components/ui/typography/Paragraph'

export default function AuthPage() {
  const [mode, setMode] = useState<'signin' | 'signup'>('signin')
  const { user, loading, initialized } = useAuthContext()
  const router = useRouter()

  useEffect(() => {
    if (initialized && user) {
      router.push('/')
    }
  }, [user, initialized, router])

  if (loading || !initialized) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="w-8 h-8 border-2 border-brand border-t-transparent rounded-full animate-spin" />
      </div>
    )
  }

  if (user) {
    return null // Will redirect
  }

  return (
    <div className="min-h-screen bg-background flex flex-col items-center justify-center p-4 relative overflow-hidden">
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-brand/5 via-transparent to-brand/10" />
      
      {/* Background pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-brand rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-brand rounded-full blur-3xl" />
      </div>

      {/* Header */}
      <motion.div
        className="text-center mb-8 z-10"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="flex items-center justify-center gap-3 mb-4">
          <div className="w-12 h-12 rounded-xl overflow-hidden">
            <img
              src="/logo.png"
              alt="NarrAgent Logo"
              className="w-full h-full object-contain"
            />
          </div>
          <Heading size={1} className="font-bold narr-agent-gradient">
            NarrAgent
          </Heading>
        </div>
        <Paragraph size="large" className="text-muted max-w-md">
          智能代理平台，让 AI 助手为您工作
        </Paragraph>
      </motion.div>

      {/* Auth Form */}
      <div className="z-10">
        <AuthForm mode={mode} onModeChange={setMode} />
      </div>

      {/* Footer */}
      <motion.div
        className="mt-8 text-center z-10"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6, delay: 0.3 }}
      >
        <Paragraph size="xsmall" className="text-muted">
          继续使用即表示您同意我们的{' '}
          <a href="/terms" className="text-brand hover:text-brand/80 underline">
            服务条款
          </a>{' '}
          和{' '}
          <a href="/privacy" className="text-brand hover:text-brand/80 underline">
            隐私政策
          </a>
        </Paragraph>
      </motion.div>
    </div>
  )
}
