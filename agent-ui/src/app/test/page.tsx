'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useFFmpeg } from '@/hooks/useFFmpeg'

export default function TestPage() {
  const [status, setStatus] = useState<string>('等待测试')
  const ffmpeg = useFFmpeg()

  const testFFmpegLoad = async () => {
    try {
      setStatus('开始测试 FFmpeg 加载...')
      
      if (!ffmpeg.loadState.isLoaded) {
        await ffmpeg.load()
      }
      
      setStatus('FFmpeg 加载成功！')
    } catch (error) {
      setStatus(`FFmpeg 加载失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  return (
    <div className="container mx-auto p-8">
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>FFmpeg 测试页面</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <p className="text-sm text-gray-600 mb-4">
              这是一个简化的测试页面，用于验证 FFmpeg 加载功能。
            </p>
            
            <Button onClick={testFFmpegLoad} disabled={ffmpeg.loadState.isLoading}>
              {ffmpeg.loadState.isLoading ? '加载中...' : '测试 FFmpeg 加载'}
            </Button>
          </div>
          
          <div className="p-4 bg-gray-100 rounded">
            <p className="font-medium">状态: {status}</p>
            <p className="text-sm text-gray-600">
              加载状态: {ffmpeg.loadState.isLoaded ? '已加载' : '未加载'}
            </p>
            <p className="text-sm text-gray-600">
              正在加载: {ffmpeg.loadState.isLoading ? '是' : '否'}
            </p>
            {ffmpeg.loadState.error && (
              <p className="text-sm text-red-600">
                错误: {ffmpeg.loadState.error}
              </p>
            )}
          </div>
          
          {ffmpeg.logs.length > 0 && (
            <div className="p-4 bg-gray-900 text-gray-100 rounded text-sm font-mono max-h-48 overflow-y-auto">
              {ffmpeg.logs.map((log, index) => (
                <div key={index}>{log}</div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
