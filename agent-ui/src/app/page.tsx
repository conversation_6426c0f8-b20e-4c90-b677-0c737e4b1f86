'use client'
import Sidebar from '@/components/playground/Sidebar/Sidebar'
import { ChatArea } from '@/components/playground/ChatArea'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { Suspense } from 'react'

export default function Home() {
  return (
    <ProtectedRoute>
      <Suspense fallback={<div>Loading...</div>}>
        <div className="flex h-screen bg-background/80">
          <Sidebar />
          <ChatArea />
        </div>
      </Suspense>
    </ProtectedRoute>
  )
}
