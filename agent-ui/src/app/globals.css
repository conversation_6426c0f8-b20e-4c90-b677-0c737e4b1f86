@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --color-border-default: 255, 255, 255, 0.2;
    --scrollbar-width: 0.1rem;

    /* shadcn/ui CSS 变量 - 深色主题 */
    --background: 17 17 19; /* #111113 */
    --foreground: 250 250 250; /* #FAFAFA */
    --primary: 250 250 250; /* #FAFAFA */
    --primary-foreground: 24 24 27; /* #18181B */
    --secondary: 39 39 42; /* #27272A */
    --secondary-foreground: 250 250 250; /* #FAFAFA */
    --accent: 39 39 42; /* #27272A */
    --accent-foreground: 250 250 250; /* #FAFAFA */
    --muted: 39 39 42; /* #27272A */
    --muted-foreground: 161 161 170; /* #A1A1AA */
    --border: 39 39 42; /* #27272A */
    --input: 39 39 42; /* #27272A */
    --ring: 79 37 184; /* #4f25b8 */
    --destructive: 229 57 53; /* #E53935 */
    --destructive-foreground: 250 250 250; /* #FAFAFA */
  }

  body {
    @apply bg-background/80 text-secondary;
  }
}

/* NarrAgent 文字渐变效果 */
.narr-agent-gradient {
  background: linear-gradient(135deg, #7ac8f9 0%, #c9c9f9 50%, #8a61f6 100%);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  animation: gradient-shift 3s ease-in-out infinite;
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(161, 161, 170, 0.3);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(161, 161, 170, 0.5);
}

::-webkit-scrollbar-thumb:active {
  background: rgba(161, 161, 170, 0.7);
}

/* 针对侧边栏的特殊滚动条样式 */
.sidebar-scroll::-webkit-scrollbar {
  width: 4px;
}

.sidebar-scroll::-webkit-scrollbar-track {
  background: transparent;
  margin: 8px 0;
}

.sidebar-scroll::-webkit-scrollbar-thumb {
  background: rgba(79, 37, 184, 0.2);
  border-radius: 2px;
  transition: all 0.2s ease;
}

.sidebar-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(79, 37, 184, 0.4);
  width: 6px;
}

/* Firefox 滚动条样式 */
.sidebar-scroll {
  scrollbar-width: thin;
  scrollbar-color: rgba(79, 37, 184, 0.2) transparent;
}

/* Toast 自定义样式 */
[data-sonner-toaster] {
  --normal-bg: #27272A !important;
  --normal-border: rgba(255, 255, 255, 0.2) !important;
  --normal-text: #FAFAFA !important;
  --success-bg: #16A34A !important;
  --success-border: #22C55E !important;
  --success-text: #FFFFFF !important;
  --error-bg: #DC2626 !important;
  --error-border: #EF4444 !important;
  --error-text: #FFFFFF !important;
  --warning-bg: #D97706 !important;
  --warning-border: #F59E0B !important;
  --warning-text: #FFFFFF !important;
  --info-bg: #2563EB !important;
  --info-border: #3B82F6 !important;
  --info-text: #FFFFFF !important;
}

[data-sonner-toast] {
  background: var(--normal-bg) !important;
  border: 1px solid var(--normal-border) !important;
  color: var(--normal-text) !important;
  border-radius: 0.75rem !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
}

[data-sonner-toast][data-type="success"] {
  background: var(--success-bg) !important;
  border-color: var(--success-border) !important;
  color: var(--success-text) !important;
}

[data-sonner-toast][data-type="error"] {
  background: var(--error-bg) !important;
  border-color: var(--error-border) !important;
  color: var(--error-text) !important;
}

[data-sonner-toast][data-type="warning"] {
  background: var(--warning-bg) !important;
  border-color: var(--warning-border) !important;
  color: var(--warning-text) !important;
}

[data-sonner-toast][data-type="info"] {
  background: var(--info-bg) !important;
  border-color: var(--info-border) !important;
  color: var(--info-text) !important;
}

[data-sonner-toast] [data-title] {
  color: inherit !important;
  font-weight: 500 !important;
}

[data-sonner-toast] [data-description] {
  color: inherit !important;
  opacity: 0.9 !important;
}

[data-sonner-toast] [data-close-button] {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: inherit !important;
}

[data-sonner-toast] [data-close-button]:hover {
  background: rgba(255, 255, 255, 0.2) !important;
}

/* 强制修复按钮文字对比度问题 */
.btn-download {
  background-color: #4f25b8 !important;
  color: #ffffff !important;
}

.btn-download:hover {
  background-color: rgba(79, 37, 184, 0.9) !important;
  color: #ffffff !important;
}

/* 确保所有按钮都有正确的文字颜色 */
/* 只对没有明确设置文字颜色的 bg-primary 按钮应用白色文字 */
button[class*="bg-primary"]:not([class*="text-"]) {
  color: #ffffff !important;
}

button[class*="bg-secondary"]:not([class*="text-"]) {
  color: #18181B !important;
}

button[class*="bg-brand"]:not([class*="text-"]) {
  color: #ffffff !important;
}

/* 科技感工作台样式增强 */
.tech-upload-area {
  position: relative;
  background: linear-gradient(135deg, rgba(79, 37, 184, 0.05) 0%, rgba(59, 130, 246, 0.05) 100%);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(79, 37, 184, 0.2);
}

.tech-upload-area::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: 1px;
  background: linear-gradient(135deg, rgba(79, 37, 184, 0.3), rgba(59, 130, 246, 0.3));
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
  -webkit-mask-composite: xor;
}

.tech-glow-border {
  box-shadow:
    0 0 20px rgba(79, 37, 184, 0.3),
    inset 0 0 20px rgba(79, 37, 184, 0.1);
}

.tech-glow-border:hover {
  box-shadow:
    0 0 30px rgba(79, 37, 184, 0.5),
    inset 0 0 30px rgba(79, 37, 184, 0.2);
}

/* 科技感粒子动画 */
@keyframes particle-float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-10px) rotate(180deg);
    opacity: 1;
  }
}

.tech-particle {
  animation: particle-float 3s ease-in-out infinite;
}

/* 科技感扫描线效果 */
@keyframes scan-line {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.tech-scan-line {
  position: relative;
  overflow: hidden;
}

.tech-scan-line::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(79, 37, 184, 0.3),
    transparent
  );
  animation: scan-line 2s linear infinite;
}

/* 科技感脉冲效果 */
@keyframes tech-pulse {
  0%, 100% {
    box-shadow: 0 0 5px rgba(79, 37, 184, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(79, 37, 184, 0.8), 0 0 30px rgba(79, 37, 184, 0.4);
  }
}

.tech-pulse {
  animation: tech-pulse 2s ease-in-out infinite;
}

/* 科技感网格背景 */
.tech-grid-bg {
  background-image:
    linear-gradient(rgba(79, 37, 184, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(79, 37, 184, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
  animation: grid-move 20s linear infinite;
}

@keyframes grid-move {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 20px 20px;
  }
}

/* 科技感按钮增强 */
.tech-button {
  position: relative;
  background: linear-gradient(135deg, rgba(79, 37, 184, 0.8), rgba(59, 130, 246, 0.8));
  border: 1px solid rgba(79, 37, 184, 0.5);
  transition: all 0.3s ease;
}

.tech-button:hover {
  background: linear-gradient(135deg, rgba(79, 37, 184, 1), rgba(59, 130, 246, 1));
  box-shadow: 0 0 20px rgba(79, 37, 184, 0.5);
  transform: translateY(-2px);
}

.tech-button::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent);
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.tech-button:hover::before {
  opacity: 1;
}

/* 响应式设计优化 */
@media (max-width: 640px) {
  .tech-upload-area {
    margin: 0.75rem;
  }

  .tech-glow-border {
    box-shadow:
      0 0 15px rgba(79, 37, 184, 0.2),
      inset 0 0 15px rgba(79, 37, 184, 0.05);
  }

  .tech-glow-border:hover {
    box-shadow:
      0 0 20px rgba(79, 37, 184, 0.3),
      inset 0 0 20px rgba(79, 37, 184, 0.1);
  }

  /* 移动端粒子效果减少 */
  .tech-particle {
    animation-duration: 4s;
  }

  /* 移动端网格背景优化 */
  .tech-grid-bg {
    background-size: 15px 15px;
  }

  /* 移动端按钮优化 */
  .tech-button:hover {
    transform: translateY(-1px);
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .tech-upload-area:hover {
    border-color: rgba(79, 37, 184, 0.5);
    background: rgba(79, 37, 184, 0.03);
    box-shadow: 0 0 15px rgba(79, 37, 184, 0.15);
  }

  .tech-button:hover {
    background: linear-gradient(135deg, rgba(79, 37, 184, 0.9), rgba(59, 130, 246, 0.9));
    transform: none;
  }

  /* 触摸设备上禁用某些动画以提升性能 */
  .tech-scan-line::after {
    animation: none;
  }

  .tech-pulse {
    animation-duration: 3s;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .tech-upload-area {
    border-width: 3px;
    border-color: rgba(79, 37, 184, 0.8);
  }

  .tech-glow-border {
    box-shadow:
      0 0 10px rgba(79, 37, 184, 0.8),
      inset 0 0 10px rgba(79, 37, 184, 0.3);
  }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
  .tech-particle,
  .tech-scan-line::after,
  .tech-pulse,
  .tech-grid-bg {
    animation: none;
  }

  .tech-button {
    transition: background-color 0.2s ease, border-color 0.2s ease;
  }

  .tech-upload-area {
    transition: border-color 0.2s ease, background-color 0.2s ease;
  }
}
