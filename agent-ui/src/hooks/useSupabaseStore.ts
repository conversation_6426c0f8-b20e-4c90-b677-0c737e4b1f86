'use client'

import { useState, useEffect } from 'react'
import { supabase } from '@/lib/supabase'
import { useAuthContext } from '@/components/auth/AuthProvider'
import { toast } from 'sonner'
import type { Database } from '@/lib/supabase'

type Agent = Database['public']['Tables']['agents']['Row']
type ChatSession = Database['public']['Tables']['chat_sessions']['Row']
type ChatMessage = Database['public']['Tables']['chat_messages']['Row']

export function useSupabaseStore() {
  const { user } = useAuthContext()
  const [agents, setAgents] = useState<Agent[]>([])
  const [sessions, setSessions] = useState<ChatSession[]>([])
  const [currentSession, setCurrentSession] = useState<ChatSession | null>(null)
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [loading, setLoading] = useState(false)

  // Fetch available agents (public + user's organization agents)
  const fetchAgents = async () => {
    try {
      setLoading(true)
      
      // For now, fetch public agents. Later we'll add organization-specific agents
      const { data, error } = await supabase
        .from('agents')
        .select('*')
        .eq('is_public', true)
        .eq('is_active', true)
        .order('created_at', { ascending: false })

      if (error) throw error

      setAgents(data || [])
    } catch (error: any) {
      console.error('Error fetching agents:', error)
      toast.error('获取代理列表失败')
    } finally {
      setLoading(false)
    }
  }

  // Fetch user's chat sessions
  const fetchSessions = async () => {
    if (!user) return

    try {
      const { data, error } = await supabase
        .from('chat_sessions')
        .select('*')
        .eq('user_id', user.id)
        .eq('is_archived', false)
        .order('updated_at', { ascending: false })

      if (error) throw error

      setSessions(data || [])
    } catch (error: any) {
      console.error('Error fetching sessions:', error)
      toast.error('获取会话列表失败')
    }
  }

  // Fetch messages for a session
  const fetchMessages = async (sessionId: string) => {
    try {
      const { data, error } = await supabase
        .from('chat_messages')
        .select('*')
        .eq('session_id', sessionId)
        .order('created_at', { ascending: true })

      if (error) throw error

      setMessages(data || [])
    } catch (error: any) {
      console.error('Error fetching messages:', error)
      toast.error('获取消息失败')
    }
  }

  // Create a new chat session
  const createSession = async (agentId: string, title?: string) => {
    if (!user) {
      toast.error('请先登录')
      return null
    }

    try {
      const { data, error } = await supabase
        .from('chat_sessions')
        .insert({
          agent_id: agentId,
          user_id: user.id,
          title: title || '新对话'
        })
        .select()
        .single()

      if (error) throw error

      const newSession = data
      setSessions(prev => [newSession, ...prev])
      setCurrentSession(newSession)
      setMessages([])
      
      return newSession
    } catch (error: any) {
      console.error('Error creating session:', error)
      toast.error('创建会话失败')
      return null
    }
  }

  // Add a message to the current session
  const addMessage = async (content: string, role: 'user' | 'assistant' | 'system', metadata?: any) => {
    if (!currentSession) {
      toast.error('请先选择一个会话')
      return null
    }

    try {
      const { data, error } = await supabase
        .from('chat_messages')
        .insert({
          session_id: currentSession.id,
          role,
          content,
          metadata: metadata || {}
        })
        .select()
        .single()

      if (error) throw error

      const newMessage = data
      setMessages(prev => [...prev, newMessage])
      
      // Update session's updated_at timestamp
      await supabase
        .from('chat_sessions')
        .update({ updated_at: new Date().toISOString() })
        .eq('id', currentSession.id)

      return newMessage
    } catch (error: any) {
      console.error('Error adding message:', error)
      toast.error('发送消息失败')
      return null
    }
  }

  // Update session title
  const updateSessionTitle = async (sessionId: string, title: string) => {
    try {
      const { error } = await supabase
        .from('chat_sessions')
        .update({ title })
        .eq('id', sessionId)

      if (error) throw error

      setSessions(prev => 
        prev.map(session => 
          session.id === sessionId ? { ...session, title } : session
        )
      )

      if (currentSession?.id === sessionId) {
        setCurrentSession(prev => prev ? { ...prev, title } : null)
      }
    } catch (error: any) {
      console.error('Error updating session title:', error)
      toast.error('更新会话标题失败')
    }
  }

  // Delete a session
  const deleteSession = async (sessionId: string) => {
    try {
      const { error } = await supabase
        .from('chat_sessions')
        .update({ is_archived: true })
        .eq('id', sessionId)

      if (error) throw error

      setSessions(prev => prev.filter(session => session.id !== sessionId))
      
      if (currentSession?.id === sessionId) {
        setCurrentSession(null)
        setMessages([])
      }

      toast.success('会话已删除')
    } catch (error: any) {
      console.error('Error deleting session:', error)
      toast.error('删除会话失败')
    }
  }

  // Switch to a different session
  const switchSession = async (session: ChatSession) => {
    setCurrentSession(session)
    await fetchMessages(session.id)
  }

  // Subscribe to real-time updates for current session
  useEffect(() => {
    if (!currentSession) return

    const subscription = supabase
      .channel(`chat_session_${currentSession.id}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'chat_messages',
          filter: `session_id=eq.${currentSession.id}`
        },
        (payload) => {
          const newMessage = payload.new as ChatMessage
          setMessages(prev => {
            // Avoid duplicates
            if (prev.some(msg => msg.id === newMessage.id)) {
              return prev
            }
            return [...prev, newMessage]
          })
        }
      )
      .subscribe()

    return () => {
      subscription.unsubscribe()
    }
  }, [currentSession])

  // Initial data fetch
  useEffect(() => {
    fetchAgents()
  }, [])

  useEffect(() => {
    if (user) {
      fetchSessions()
    } else {
      setSessions([])
      setCurrentSession(null)
      setMessages([])
    }
  }, [user])

  return {
    // Data
    agents,
    sessions,
    currentSession,
    messages,
    loading,
    
    // Actions
    fetchAgents,
    fetchSessions,
    fetchMessages,
    createSession,
    addMessage,
    updateSessionTitle,
    deleteSession,
    switchSession
  }
}
