import { useState, useCallback, useRef, useEffect } from 'react'
import { ffmpegService, ffmpegOperations } from '@/lib/ffmpeg'
import type {
  FFmpegLoadState,
  ProcessingProgress,
  ProcessingStatus,
  MediaProcessingTask,
  MediaProcessingType,
  ProcessingOptions,
  MediaProcessingResult
} from '@/types/media'

/**
 * FFmpeg Hook 返回值类型
 */
interface UseFFmpegReturn {
  // 状态
  loadState: FFmpegLoadState
  processingStatus: ProcessingStatus
  progress: ProcessingProgress | null
  logs: string[]
  
  // 方法
  load: (useMultithread?: boolean) => Promise<void>
  unload: () => Promise<void>
  processMedia: (
    file: File,
    type: MediaProcessingType,
    options: ProcessingOptions
  ) => Promise<MediaProcessingResult>
  
  // 便捷方法
  convertVideo: (file: File, format: string, options?: any) => Promise<MediaProcessingResult>
  convertAudio: (file: File, format: string, options?: any) => Promise<MediaProcessingResult>
  trimVideo: (file: File, startTime: string, duration: string) => Promise<MediaProcessingResult>
  extractAudio: (file: File, format?: string) => Promise<MediaProcessingResult>
  
  // 工具方法
  clearLogs: () => void
  downloadResult: (result: MediaProcessingResult, filename: string) => void
}

/**
 * FFmpeg React Hook
 * 提供 React 组件友好的 FFmpeg 接口
 */
export function useFFmpeg(): UseFFmpegReturn {
  // 状态管理
  const [loadState, setLoadState] = useState<FFmpegLoadState>({
    isLoaded: false,
    isLoading: false
  })
  
  const [processingStatus, setProcessingStatus] = useState<ProcessingStatus>('idle')
  const [progress, setProgress] = useState<ProcessingProgress | null>(null)
  const [logs, setLogs] = useState<string[]>([])
  
  // 引用
  const logsRef = useRef<string[]>([])
  const abortControllerRef = useRef<AbortController | null>(null)

  // 更新日志
  const addLog = useCallback((message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    const logEntry = `[${timestamp}] ${message}`
    
    logsRef.current = [...logsRef.current.slice(-99), logEntry] // 保留最近100条日志
    setLogs([...logsRef.current])
  }, [])

  // 清空日志
  const clearLogs = useCallback(() => {
    logsRef.current = []
    setLogs([])
  }, [])

  // 进度回调
  const onProgress = useCallback((progressValue: number, time: number) => {
    const progressData: ProcessingProgress = {
      progress: progressValue,
      time,
      speed: progressValue > 0 ? time / (progressValue * 1000000) : 0
    }
    
    setProgress(progressData)
    addLog(`Progress: ${(progressValue * 100).toFixed(1)}% (${(time / 1000000).toFixed(2)}s)`)
  }, [addLog])

  // 日志回调
  const onLog = useCallback((message: string) => {
    addLog(message)
  }, [addLog])

  // 加载 FFmpeg
  const load = useCallback(async (useMultithread = true, retryCount = 0) => {
    const maxRetries = 2

    // 如果已经加载完成，直接返回
    if (loadState.isLoaded && ffmpegService.isFFmpegLoaded()) {
      addLog('FFmpeg 已加载')
      return
    }

    // 如果正在加载，等待加载完成而不是抛出错误
    if (loadState.isLoading) {
      addLog('FFmpeg 正在加载中，等待加载完成...')

      // 轮询等待加载完成
      return new Promise<void>((resolve, reject) => {
        let attempts = 0
        const maxAttempts = 300 // 30秒超时

        const checkLoadState = () => {
          attempts++

          if (loadState.isLoaded) {
            resolve()
          } else if (loadState.error) {
            reject(new Error(loadState.error))
          } else if (loadState.isLoading && attempts < maxAttempts) {
            // 继续等待
            setTimeout(checkLoadState, 100)
          } else {
            // 超时或状态异常
            reject(new Error('FFmpeg 加载超时'))
          }
        }
        checkLoadState()
      })
    }

    setLoadState({ isLoaded: false, isLoading: true })
    addLog(`开始加载 FFmpeg ${useMultithread ? '(优先多线程)' : '(单线程)'}... ${retryCount > 0 ? `(重试 ${retryCount}/${maxRetries})` : ''}`)

    try {
      await ffmpegService.load(useMultithread, onProgress, onLog)

      // 简单验证加载状态
      if (!ffmpegService.isFFmpegLoaded()) {
        throw new Error('FFmpeg 加载验证失败')
      }

      setLoadState({ isLoaded: true, isLoading: false })
      addLog('FFmpeg 加载完成并验证成功')
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误'

      // 如果是第一次尝试且使用多线程，尝试单线程版本
      if (retryCount === 0 && useMultithread && errorMessage.includes('多线程')) {
        addLog(`多线程版本失败: ${errorMessage}，尝试单线程版本...`)
        setLoadState({ isLoaded: false, isLoading: false })
        return load(false, retryCount + 1)
      }

      // 如果还有重试次数，进行重试
      if (retryCount < maxRetries) {
        addLog(`加载失败: ${errorMessage}，准备重试...`)
        setLoadState({ isLoaded: false, isLoading: false })
        // 延迟重试
        await new Promise(resolve => setTimeout(resolve, 1000))
        return load(useMultithread, retryCount + 1)
      }

      // 所有重试都失败了
      setLoadState({
        isLoaded: false,
        isLoading: false,
        error: errorMessage
      })
      addLog(`FFmpeg 加载最终失败: ${errorMessage}`)
      throw error
    }
  }, [loadState, onProgress, onLog, addLog])

  // 卸载 FFmpeg
  const unload = useCallback(async () => {
    if (!loadState.isLoaded) {
      return
    }

    try {
      await ffmpegService.terminate()
      setLoadState({ isLoaded: false, isLoading: false })
      setProcessingStatus('idle')
      setProgress(null)
      addLog('FFmpeg 已卸载')
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误'
      addLog(`FFmpeg 卸载失败: ${errorMessage}`)
      throw error
    }
  }, [loadState.isLoaded, addLog])

  // 通用媒体处理方法
  const processMedia = useCallback(async (
    file: File,
    type: MediaProcessingType,
    options: ProcessingOptions
  ): Promise<MediaProcessingResult> => {
    // 检查 FFmpeg 是否已加载
    if (!loadState.isLoaded) {
      addLog('FFmpeg 未就绪，尝试重新加载...')
      try {
        await load()
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '未知错误'
        addLog(`FFmpeg 重新加载失败: ${errorMessage}`)
        throw new Error(`FFmpeg 未加载: ${errorMessage}`)
      }
    }

    // 创建中止控制器
    abortControllerRef.current = new AbortController()
    
    setProcessingStatus('processing')
    setProgress({ progress: 0, time: 0 })
    
    const startTime = Date.now()
    addLog(`开始处理文件: ${file.name} (${type})`)

    try {
      let result: Uint8Array

      // 根据处理类型调用相应的方法
      switch (type) {
        case 'convert':
          if ('format' in options) {
            if (file.type.startsWith('video/')) {
              result = await ffmpegOperations.convertVideo(file, options.format, {
                quality: options.quality,
                resolution: 'resolution' in options ? options.resolution : undefined,
                bitrate: 'bitrate' in options ? options.bitrate : undefined
              })
            } else if (file.type.startsWith('audio/')) {
              result = await ffmpegOperations.convertAudio(file, options.format, {
                bitrate: 'bitrate' in options ? options.bitrate : undefined,
                sampleRate: 'sampleRate' in options ? options.sampleRate : undefined
              })
            } else {
              throw new Error('不支持的文件类型')
            }
          } else {
            throw new Error('缺少格式参数')
          }
          break

        case 'trim':
          if ('startTime' in options && 'duration' in options && options.startTime && options.duration) {
            result = await ffmpegOperations.trimVideo(file, options.startTime, options.duration)
          } else {
            throw new Error('缺少剪辑参数')
          }
          break

        case 'extract':
          if ('format' in options) {
            result = await ffmpegOperations.extractAudio(file, options.format as any)
          } else {
            result = await ffmpegOperations.extractAudio(file)
          }
          break

        default:
          throw new Error(`不支持的处理类型: ${type}`)
      }

      const processingTime = Date.now() - startTime
      const resultData: MediaProcessingResult = {
        success: true,
        data: result,
        size: result.length,
        processingTime
      }

      setProcessingStatus('completed')
      addLog(`处理完成，耗时 ${(processingTime / 1000).toFixed(2)} 秒`)
      
      return resultData

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误'
      const resultData: MediaProcessingResult = {
        success: false,
        error: errorMessage,
        processingTime: Date.now() - startTime
      }

      setProcessingStatus('error')
      addLog(`处理失败: ${errorMessage}`)
      
      return resultData
    } finally {
      abortControllerRef.current = null
    }
  }, [loadState.isLoaded, addLog])

  // 便捷方法：视频转换
  const convertVideo = useCallback(async (
    file: File,
    format: string,
    options: any = {}
  ): Promise<MediaProcessingResult> => {
    return processMedia(file, 'convert', { format, ...options })
  }, [processMedia])

  // 便捷方法：音频转换
  const convertAudio = useCallback(async (
    file: File,
    format: string,
    options: any = {}
  ): Promise<MediaProcessingResult> => {
    return processMedia(file, 'convert', { format, ...options })
  }, [processMedia])

  // 便捷方法：视频剪辑
  const trimVideo = useCallback(async (
    file: File,
    startTime: string,
    duration: string
  ): Promise<MediaProcessingResult> => {
    return processMedia(file, 'trim', { startTime, duration })
  }, [processMedia])

  // 便捷方法：提取音频
  const extractAudio = useCallback(async (
    file: File,
    format = 'mp3'
  ): Promise<MediaProcessingResult> => {
    return processMedia(file, 'extract', { format: format as any })
  }, [processMedia])

  // 下载处理结果
  const downloadResult = useCallback((result: MediaProcessingResult, filename: string) => {
    if (!result.success || !result.data) {
      addLog('无法下载：处理结果无效')
      return
    }

    try {
      const blob = new Blob([result.data])
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = filename
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
      
      addLog(`文件已下载: ${filename}`)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误'
      addLog(`下载失败: ${errorMessage}`)
    }
  }, [addLog])

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [])

  return {
    // 状态
    loadState,
    processingStatus,
    progress,
    logs,
    
    // 方法
    load,
    unload,
    processMedia,
    
    // 便捷方法
    convertVideo,
    convertAudio,
    trimVideo,
    extractAudio,
    
    // 工具方法
    clearLogs,
    downloadResult
  }
}

/**
 * 批量处理 Hook
 */
export function useBatchFFmpeg() {
  const [tasks, setTasks] = useState<MediaProcessingTask[]>([])
  const [isProcessing, setIsProcessing] = useState(false)
  const [currentTaskIndex, setCurrentTaskIndex] = useState(0)
  
  const ffmpeg = useFFmpeg()

  const addTask = useCallback((
    file: File,
    type: MediaProcessingType,
    options: ProcessingOptions
  ) => {
    const task: MediaProcessingTask = {
      id: `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      inputFile: file,
      outputFormat: 'format' in options ? options.format : 'mp4',
      options,
      status: 'idle',
      createdAt: new Date()
    }
    
    setTasks(prev => [...prev, task])
    return task.id
  }, [])

  const removeTask = useCallback((taskId: string) => {
    setTasks(prev => prev.filter(task => task.id !== taskId))
  }, [])

  const clearTasks = useCallback(() => {
    setTasks([])
    setCurrentTaskIndex(0)
  }, [])

  const processBatch = useCallback(async () => {
    if (tasks.length === 0 || isProcessing) {
      return
    }

    setIsProcessing(true)
    
    try {
      if (!ffmpeg.loadState.isLoaded) {
        await ffmpeg.load()
      }

      for (let i = 0; i < tasks.length; i++) {
        setCurrentTaskIndex(i)
        const task = tasks[i]
        
        setTasks(prev => prev.map(t => 
          t.id === task.id ? { ...t, status: 'processing' as ProcessingStatus } : t
        ))

        try {
          const result = await ffmpeg.processMedia(task.inputFile, task.type, task.options)
          
          setTasks(prev => prev.map(t => 
            t.id === task.id 
              ? { 
                  ...t, 
                  status: result.success ? 'completed' : 'error',
                  result: result.data,
                  error: result.error,
                  completedAt: new Date()
                } 
              : t
          ))
        } catch (error) {
          setTasks(prev => prev.map(t => 
            t.id === task.id 
              ? { 
                  ...t, 
                  status: 'error',
                  error: error instanceof Error ? error.message : '未知错误',
                  completedAt: new Date()
                } 
              : t
          ))
        }
      }
    } finally {
      setIsProcessing(false)
      setCurrentTaskIndex(0)
    }
  }, [tasks, isProcessing, ffmpeg])

  return {
    tasks,
    isProcessing,
    currentTaskIndex,
    addTask,
    removeTask,
    clearTasks,
    processBatch,
    ffmpeg
  }
}
