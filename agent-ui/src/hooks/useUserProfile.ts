'use client'

import { useState, useEffect } from 'react'
import { supabase } from '@/lib/supabase'
import { useAuthContext } from '@/components/auth/AuthProvider'
import type { Database } from '@/lib/supabase'

type UserProfile = Database['public']['Tables']['user_profiles']['Row']
type UserProfileInsert = Database['public']['Tables']['user_profiles']['Insert']
type UserProfileUpdate = Database['public']['Tables']['user_profiles']['Update']

export function useUserProfile() {
  const { user } = useAuthContext()
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch user profile
  const fetchProfile = async () => {
    if (!user) {
      setProfile(null)
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', user.id)
        .single()

      if (error) {
        if (error.code === 'PGRST116') {
          // Profile doesn't exist, create one
          console.log('Profile not found, creating new profile for user:', user.id)
          try {
            const newProfile = await createProfile({
              id: user.id,
              display_name: user.user_metadata?.display_name || user.email?.split('@')[0] || null,
              avatar_url: user.user_metadata?.avatar_url || null
            })
            console.log('Profile created successfully:', newProfile)
            return
          } catch (createError: any) {
            console.error('Failed to create profile:', createError)
            // If profile creation fails, set a default profile state
            setProfile({
              id: user.id,
              display_name: user.email?.split('@')[0] || 'User',
              avatar_url: null,
              preferences: {},
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            })
            setError('Profile creation failed, using default profile')
            return
          }
        }
        console.error('Supabase error fetching profile:', error)
        throw error
      }

      setProfile(data)
    } catch (err: any) {
      console.error('Error fetching profile:', err)
      setError(err.message)
      // Set a fallback profile to prevent app from breaking
      setProfile({
        id: user.id,
        display_name: user.email?.split('@')[0] || 'User',
        avatar_url: null,
        preferences: {},
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
    } finally {
      setLoading(false)
    }
  }

  // Create user profile
  const createProfile = async (profileData: UserProfileInsert) => {
    try {
      setError(null)

      console.log('Creating profile with data:', profileData)

      const { data, error } = await supabase
        .from('user_profiles')
        .insert(profileData)
        .select()
        .single()

      if (error) {
        console.error('Supabase error creating profile:', error)
        throw error
      }

      console.log('Profile created successfully:', data)
      setProfile(data)
      return data
    } catch (err: any) {
      console.error('Error creating profile:', err)
      setError(err.message || 'Failed to create profile')
      throw err
    }
  }

  // Update user profile
  const updateProfile = async (updates: UserProfileUpdate) => {
    if (!user) throw new Error('No user logged in')

    try {
      setError(null)

      const { data, error } = await supabase
        .from('user_profiles')
        .update(updates)
        .eq('id', user.id)
        .select()
        .single()

      if (error) throw error

      setProfile(data)
      return data
    } catch (err: any) {
      console.error('Error updating profile:', err)
      setError(err.message)
      throw err
    }
  }

  // Upload avatar
  const uploadAvatar = async (file: File) => {
    if (!user) throw new Error('No user logged in')

    try {
      setError(null)

      // Upload file to storage
      const fileExt = file.name.split('.').pop()
      const fileName = `${user.id}/avatar.${fileExt}`
      const filePath = `avatars/${fileName}`

      const { error: uploadError } = await supabase.storage
        .from('avatars')
        .upload(filePath, file, { upsert: true })

      if (uploadError) throw uploadError

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('avatars')
        .getPublicUrl(filePath)

      // Update profile with new avatar URL
      await updateProfile({ avatar_url: publicUrl })

      return publicUrl
    } catch (err: any) {
      console.error('Error uploading avatar:', err)
      setError(err.message)
      throw err
    }
  }

  // Delete avatar
  const deleteAvatar = async () => {
    if (!user || !profile?.avatar_url) return

    try {
      setError(null)

      // Extract file path from URL
      const url = new URL(profile.avatar_url)
      const filePath = url.pathname.split('/storage/v1/object/public/avatars/')[1]

      if (filePath) {
        const { error: deleteError } = await supabase.storage
          .from('avatars')
          .remove([filePath])

        if (deleteError) {
          console.warn('Error deleting avatar file:', deleteError)
        }
      }

      // Update profile to remove avatar URL
      await updateProfile({ avatar_url: null })
    } catch (err: any) {
      console.error('Error deleting avatar:', err)
      setError(err.message)
      throw err
    }
  }

  useEffect(() => {
    fetchProfile()
  }, [user])

  return {
    profile,
    loading,
    error,
    fetchProfile,
    createProfile,
    updateProfile,
    uploadAvatar,
    deleteAvatar
  }
}
