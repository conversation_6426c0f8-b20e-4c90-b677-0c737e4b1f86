'use client'

import { useEffect } from 'react'

/**
 * HydrationFix 组件用于解决 React hydration 不匹配问题
 * 
 * 这个组件主要解决以下问题：
 * 1. 浏览器扩展可能在运行时向 HTML 元素添加属性（如 tracking="true"）
 * 2. 这些属性在服务端渲染时不存在，但在客户端渲染时存在，导致 hydration 不匹配
 * 3. 通过在客户端渲染后清理这些不必要的属性来解决问题
 */
export function HydrationFix() {
  useEffect(() => {
    // 在客户端渲染完成后，清理可能由浏览器扩展添加的属性
    const cleanupAttributes = () => {
      const htmlElement = document.documentElement
      
      // 移除可能由浏览器扩展添加的属性
      const attributesToRemove = [
        'tracking',
        'data-tracking',
        'data-extension',
        'data-adblock',
        'data-privacy'
      ]
      
      attributesToRemove.forEach(attr => {
        if (htmlElement.hasAttribute(attr)) {
          htmlElement.removeAttribute(attr)
        }
      })
    }
    
    // 立即执行一次清理
    cleanupAttributes()
    
    // 使用 MutationObserver 监听属性变化，如果有扩展再次添加属性，立即移除
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.target === document.documentElement) {
          const attributeName = mutation.attributeName
          if (attributeName && ['tracking', 'data-tracking', 'data-extension', 'data-adblock', 'data-privacy'].includes(attributeName)) {
            document.documentElement.removeAttribute(attributeName)
          }
        }
      })
    })
    
    // 开始监听 html 元素的属性变化
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['tracking', 'data-tracking', 'data-extension', 'data-adblock', 'data-privacy']
    })
    
    // 清理函数
    return () => {
      observer.disconnect()
    }
  }, [])
  
  // 这个组件不渲染任何内容
  return null
}
