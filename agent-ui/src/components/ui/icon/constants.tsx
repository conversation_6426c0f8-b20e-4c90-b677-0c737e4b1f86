import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ogo,
  Aws<PERSON>ogo,
  Azure<PERSON>ogo,
  Anthropic<PERSON>ogo,
  GroqLogo,
  FireworksLogo,
  DeepseekLogo,
  CohereLogo,
  OllamaLogo,
  XaiLogo,
  AgnoIcon,
  UserIcon,
  AgentIcon,
  SheetIcon,
  NextjsTag,
  ShadcnTag,
  TailwindTag,
  AgnoTag,
  ReasoningIcon,
  ReferencesIcon
} from './custom-icons'
import { IconTypeMap } from './types'
import {
  RefreshCw,
  Edit,
  Save,
  X,
  ArrowDown,
  SendIcon,
  Download,
  HammerIcon,
  Check,
  ChevronDown,
  ChevronUp,
  Trash,
  Settings,
  Play,
  Loader,
  Clock,
  Info,
  Video,
  Music,
  Shield,
  Layers,
  Construction,
  Cpu,
  Upload,
  File,
  Terminal,
  Zap
} from 'lucide-react'

import { PlusIcon } from '@radix-ui/react-icons'

export const ICONS: IconTypeMap = {
  'open-ai': OpenAILogo,
  mistral: MistralLogo,
  gemini: GeminiLogo,
  aws: Aws<PERSON>ogo,
  azure: Azure<PERSON>ogo,
  anthropic: An<PERSON><PERSON><PERSON><PERSON>,
  groq: <PERSON><PERSON><PERSON><PERSON><PERSON>,
  fireworks: FireworksLogo,
  deepseek: Deepseek<PERSON><PERSON>,
  cohere: Cohere<PERSON>ogo,
  ollama: OllamaLogo,
  xai: Xai<PERSON>ogo,
  agno: AgnoIcon,
  user: UserIcon,
  agent: AgentIcon,
  sheet: SheetIcon,
  nextjs: NextjsTag,
  shadcn: ShadcnTag,
  tailwind: TailwindTag,
  reasoning: ReasoningIcon,
  'agno-tag': AgnoTag,
  refresh: RefreshCw,
  edit: Edit,
  save: Save,
  x: X,
  'arrow-down': ArrowDown,
  send: SendIcon,
  download: Download,
  hammer: HammerIcon,
  check: Check,
  'chevron-down': ChevronDown,
  'chevron-up': ChevronUp,
  'plus-icon': PlusIcon,
  references: ReferencesIcon,
  trash: Trash,
  settings: Settings,
  play: Play,
  loader: Loader,
  clock: Clock,
  info: Info,
  video: Video,
  music: Music,
  shield: Shield,
  layers: Layers,
  construction: Construction,
  cpu: Cpu,
  upload: Upload,
  file: File,
  terminal: Terminal,
  zap: Zap
}
