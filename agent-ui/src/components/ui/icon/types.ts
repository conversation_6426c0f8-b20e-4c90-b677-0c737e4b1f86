import { type ElementType } from 'react'

export type IconType =
  | 'mistral'
  | 'gemini'
  | 'aws'
  | 'azure'
  | 'anthropic'
  | 'groq'
  | 'fireworks'
  | 'deepseek'
  | 'cohere'
  | 'ollama'
  | 'xai'
  | 'agno'
  | 'user'
  | 'agent'
  | 'open-ai'
  | 'sheet'
  | 'nextjs'
  | 'shadcn'
  | 'tailwind'
  | 'reasoning'
  | 'agno-tag'
  | 'refresh'
  | 'edit'
  | 'save'
  | 'x'
  | 'arrow-down'
  | 'send'
  | 'download'
  | 'hammer'
  | 'check'
  | 'chevron-down'
  | 'chevron-up'
  | 'plus-icon'
  | 'references'
  | 'trash'
  | 'settings'
  | 'play'
  | 'loader'
  | 'clock'
  | 'info'
  | 'video'
  | 'music'
  | 'shield'
  | 'layers'
  | 'construction'
  | 'cpu'
  | 'upload'
  | 'file'
  | 'terminal'
  | 'zap'

export type IconColor =
  | 'primary'
  | 'primaryAccent'
  | 'secondary'
  | 'muted'
  | 'brand'
  | 'destructive'
  | 'positive'
  | 'accent'
  | 'primary-contrast'
  | 'secondary-contrast'

export interface IconProps {
  type: IconType
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'dot' | 'xxs' | 'default'
  className?: string
  color?: IconColor
  disabled?: boolean
}

export type IconTypeMap = {
  [key in IconType]: ElementType
}
