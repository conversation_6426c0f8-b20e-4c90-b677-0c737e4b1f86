import { type FC } from 'react'

import { cn } from '@/lib/utils'

import { ICONS } from './constants'
import { type IconProps } from './types'

// 颜色映射，确保在暗色主题下有良好的对比度
const colorMap = {
  primary: 'text-primary', // #FAFAFA - 白色
  primaryAccent: 'text-primaryAccent', // #18181B - 深色
  secondary: 'text-secondary', // #f5f5f5 - 浅色
  muted: 'text-muted', // #A1A1AA - 灰色
  brand: 'text-brand', // #4f25b8 - 品牌色
  destructive: 'text-destructive', // #E53935 - 红色
  positive: 'text-positive', // #22C55E - 绿色
  accent: 'text-accent', // #27272A - 深灰色
  // 为暗色主题优化的颜色
  'primary-contrast': 'text-primaryAccent', // 在浅色背景上使用深色
  'secondary-contrast': 'text-primary', // 在深色背景上使用浅色
} as const

const Icon: FC<IconProps> = ({
  type,
  size = 'sm',
  className,
  color,
  disabled = false
}) => {
  const IconElement = ICONS[type]

  // 获取颜色类，如果没有指定颜色或颜色不在映射中，使用默认的primary
  const colorClass = color && !disabled && color in colorMap
    ? colorMap[color as keyof typeof colorMap]
    : 'text-primary'

  return (
    <IconElement
      className={cn(
        colorClass,
        disabled && 'cursor-default text-muted/50',
        className,
        size === 'xxs' && 'size-3',
        size === 'xs' && 'size-4',
        size === 'sm' && 'size-6',
        size === 'md' && 'size-[42px]',
        size === 'lg' && 'size-7',
        size === 'dot' && 'size-[5.07px]',
        size === 'default' && ' '
      )}
    />
  )
}

export default Icon
