'use client'

import { useState, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import Icon from '@/components/ui/icon'
import { motion, Reorder } from 'framer-motion'
import { toast } from 'sonner'
import { type VideoFileInfo, formatFileSize } from '@/types/narration'

interface VideoSorterProps {
  files: VideoFileInfo[]
  onFilesReorder: (files: VideoFileInfo[]) => void
  onMergeRequest: () => void
  isMerging?: boolean
  className?: string
}

export default function VideoSorter({
  files,
  onFilesReorder,
  onMergeRequest,
  isMerging = false,
  className
}: VideoSorterProps) {
  const [editingOrder, setEditingOrder] = useState<string | null>(null)
  const [tempOrder, setTempOrder] = useState<string>('')

  // 拖拽重排序
  const handleReorder = useCallback((newFiles: VideoFileInfo[]) => {
    const reorderedFiles = newFiles.map((file, index) => ({
      ...file,
      order: index + 1
    }))
    onFilesReorder(reorderedFiles)
  }, [onFilesReorder])

  // 开始编辑排序
  const startEditOrder = useCallback((fileId: string, currentOrder: number) => {
    setEditingOrder(fileId)
    setTempOrder(currentOrder.toString())
  }, [])

  // 保存排序编辑
  const saveOrderEdit = useCallback((fileId: string) => {
    const newOrder = parseInt(tempOrder)
    if (isNaN(newOrder) || newOrder < 1 || newOrder > files.length) {
      toast.error(`排序号必须在 1-${files.length} 之间`)
      return
    }

    const updatedFiles = [...files]
    const fileIndex = updatedFiles.findIndex(f => f.id === fileId)
    if (fileIndex === -1) return

    // 移除当前文件
    const [file] = updatedFiles.splice(fileIndex, 1)
    
    // 插入到新位置
    updatedFiles.splice(newOrder - 1, 0, { ...file, order: newOrder })
    
    // 重新计算所有文件的排序
    const reorderedFiles = updatedFiles.map((f, index) => ({
      ...f,
      order: index + 1
    }))

    onFilesReorder(reorderedFiles)
    setEditingOrder(null)
    toast.success('排序已更新')
  }, [files, tempOrder, onFilesReorder])

  // 取消编辑
  const cancelEdit = useCallback(() => {
    setEditingOrder(null)
    setTempOrder('')
  }, [])

  // 移除文件
  const removeFile = useCallback((fileId: string) => {
    const newFiles = files.filter(f => f.id !== fileId)
    const reorderedFiles = newFiles.map((file, index) => ({
      ...file,
      order: index + 1
    }))
    onFilesReorder(reorderedFiles)
    toast.success('文件已移除')
  }, [files, onFilesReorder])

  // 计算总时长和大小
  const totalSize = files.reduce((sum, file) => sum + file.size, 0)
  const totalDuration = files.reduce((sum, file) => sum + (file.duration || 0), 0)

  if (files.length === 0) {
    return null
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <Icon type="layers" size="sm" />
              视频排序 ({files.length} 个文件)
            </span>
            <Button
              onClick={onMergeRequest}
              disabled={files.length < 2 || isMerging}
              className="flex items-center gap-2"
            >
              {isMerging ? (
                <>
                  <Icon type="loader" size="xs" className="animate-spin" />
                  合并中...
                </>
              ) : (
                <>
                  <Icon type="layers" size="xs" />
                  一键合并
                </>
              )}
            </Button>
          </CardTitle>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* 统计信息 */}
          <div className="grid grid-cols-2 gap-4 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
            <div className="text-center">
              <div className="text-lg font-semibold">{formatFileSize(totalSize)}</div>
              <div className="text-xs text-gray-500">总大小</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold">
                {totalDuration > 0 ? `${Math.round(totalDuration)}s` : '--'}
              </div>
              <div className="text-xs text-gray-500">总时长</div>
            </div>
          </div>

          {/* 排序说明 */}
          <div className="text-xs text-gray-500 bg-blue-50 dark:bg-blue-950/20 p-3 rounded-lg">
            <div className="flex items-center gap-2 mb-1">
              <Icon type="info" size="xs" />
              <span className="font-medium">排序说明</span>
            </div>
            <ul className="space-y-1 ml-4">
              <li>• 拖拽文件卡片可以调整播放顺序</li>
              <li>• 点击序号可以手动编辑排序</li>
              <li>• 排序完成后点击"一键合并"按钮</li>
            </ul>
          </div>

          {/* 文件列表 */}
          <Reorder.Group
            axis="y"
            values={files}
            onReorder={handleReorder}
            className="space-y-2"
          >
            {files.map((file) => (
              <Reorder.Item
                key={file.id}
                value={file}
                className="cursor-grab active:cursor-grabbing"
              >
                <motion.div
                  layout
                  className="flex items-center gap-3 p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg hover:shadow-md transition-shadow"
                >
                  {/* 拖拽手柄 */}
                  <div className="flex items-center gap-2">
                    <Icon type="layers" size="sm" className="text-gray-400" />
                    
                    {/* 排序号 */}
                    {editingOrder === file.id ? (
                      <div className="flex items-center gap-1">
                        <Input
                          value={tempOrder}
                          onChange={(e) => setTempOrder(e.target.value)}
                          className="w-12 h-8 text-center text-sm"
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              saveOrderEdit(file.id)
                            } else if (e.key === 'Escape') {
                              cancelEdit()
                            }
                          }}
                          autoFocus
                        />
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => saveOrderEdit(file.id)}
                          className="h-8 w-8 p-0"
                        >
                          <Icon type="check" size="xs" />
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={cancelEdit}
                          className="h-8 w-8 p-0"
                        >
                          <Icon type="x" size="xs" />
                        </Button>
                      </div>
                    ) : (
                      <button
                        onClick={() => startEditOrder(file.id, file.order)}
                        className="w-8 h-8 bg-blue-100 dark:bg-blue-900/50 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-sm font-medium hover:bg-blue-200 dark:hover:bg-blue-900/70 transition-colors"
                      >
                        {file.order}
                      </button>
                    )}
                  </div>

                  {/* 文件信息 */}
                  <div className="flex items-center gap-3 flex-1 min-w-0">
                    <Icon type="video" size="sm" className="text-blue-500 flex-shrink-0" />
                    <div className="min-w-0 flex-1">
                      <p className="text-sm font-medium truncate">{file.name}</p>
                      <div className="flex items-center gap-4 text-xs text-gray-500">
                        <span>{formatFileSize(file.size)}</span>
                        {file.duration && (
                          <span>{Math.round(file.duration)}s</span>
                        )}
                        <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs">
                          {file.status === 'pending' && '待处理'}
                          {file.status === 'processing' && '处理中'}
                          {file.status === 'completed' && '已完成'}
                          {file.status === 'error' && '错误'}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* 操作按钮 */}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeFile(file.id)}
                    className="flex-shrink-0"
                  >
                    <Icon type="trash" size="xs" />
                  </Button>
                </motion.div>
              </Reorder.Item>
            ))}
          </Reorder.Group>
        </CardContent>
      </Card>
    </div>
  )
}
