'use client'

import { useState, useCallback, useEffect } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import Icon from '@/components/ui/icon'
import { motion, AnimatePresence } from 'framer-motion'
import { toast } from 'sonner'
import VideoUploader from './VideoUploader'
import VideoSorter from './VideoSorter'
import { useFFmpeg } from '@/hooks/useFFmpeg'
import { ffmpegService } from '@/lib/ffmpeg'
import { 
  type NarrationMode, 
  type VideoFileInfo, 
  type ProcessingState,
  type AudioExtractionResult,
  getNarrationModeConfig,
  formatFileSize
} from '@/types/narration'

interface NarrationWorkspaceProps {
  mode: NarrationMode
  className?: string
}

export default function NarrationWorkspace({ mode, className }: NarrationWorkspaceProps) {
  const [files, setFiles] = useState<VideoFileInfo[]>([])
  const [processingState, setProcessingState] = useState<ProcessingState>({
    isLoading: false,
    isProcessing: false,
    progress: 0,
    currentStep: ''
  })
  const [audioResult, setAudioResult] = useState<AudioExtractionResult | null>(null)
  const [isMerging, setIsMerging] = useState(false)

  const ffmpeg = useFFmpeg()
  const modeConfig = getNarrationModeConfig(mode)

  // 移除自动加载逻辑，避免竞态条件
  // FFmpeg 将在需要时按需加载

  // 确保 FFmpeg 就绪的统一方法
  const ensureFFmpegReady = useCallback(async (): Promise<void> => {
    // 如果已经加载完成，直接返回
    if (ffmpeg.loadState.isLoaded && ffmpegService.isFFmpegLoaded()) {
      return
    }

    // 如果正在加载，等待加载完成
    if (ffmpeg.loadState.isLoading) {
      console.log('FFmpeg 正在加载中，等待加载完成...')

      // 等待加载完成，最多等待30秒
      const maxWaitTime = 30000 // 30秒
      const checkInterval = 200 // 200ms检查一次
      const maxAttempts = maxWaitTime / checkInterval

      for (let attempt = 0; attempt < maxAttempts; attempt++) {
        await new Promise(resolve => setTimeout(resolve, checkInterval))

        if (ffmpeg.loadState.isLoaded && ffmpegService.isFFmpegLoaded()) {
          console.log('FFmpeg 加载完成')
          return
        }

        if (ffmpeg.loadState.error) {
          throw new Error(`FFmpeg 加载失败: ${ffmpeg.loadState.error}`)
        }
      }

      throw new Error('FFmpeg 加载超时')
    }

    // 开始加载 FFmpeg
    try {
      setProcessingState({
        isLoading: true,
        isProcessing: false,
        progress: 0,
        currentStep: '正在加载 FFmpeg 处理引擎...'
      })

      await ffmpeg.load()

      // 额外验证 FFmpeg 是否真正可用
      if (!ffmpegService.isFFmpegLoaded()) {
        throw new Error('FFmpeg 加载验证失败')
      }

      setProcessingState({
        isLoading: false,
        isProcessing: false,
        progress: 0,
        currentStep: ''
      })

      console.log('FFmpeg 加载并验证完成')
    } catch (error) {
      console.error('FFmpeg 加载失败:', error)
      setProcessingState({
        isLoading: false,
        isProcessing: false,
        progress: 0,
        currentStep: '',
        error: error instanceof Error ? error.message : '未知错误'
      })
      throw error
    }
  }, [ffmpeg, ffmpegService])

  // 加载 FFmpeg（保持向后兼容）
  const loadFFmpeg = useCallback(async () => {
    try {
      await ensureFFmpegReady()
      toast.success('FFmpeg 加载完成，可以开始处理视频')
    } catch (error) {
      toast.error('FFmpeg 加载失败，请刷新页面重试')
      throw error
    }
  }, [ensureFFmpegReady])

  // 提取音频
  const extractAudio = useCallback(async (videoFile: VideoFileInfo) => {
    try {
      // 确保 FFmpeg 就绪
      await ensureFFmpegReady()
      setProcessingState({
        isLoading: false,
        isProcessing: true,
        progress: 0,
        currentStep: '正在提取音频...'
      })

      // 监听进度
      const progressHandler = (progress: number, time: number) => {
        setProcessingState(prev => ({
          ...prev,
          progress: progress * 100,
          currentStep: `正在提取音频... ${(progress * 100).toFixed(1)}%`
        }))
      }

      // 进度监听通过 hook 已经处理

      const result = await ffmpeg.extractAudio(videoFile.file, 'mp3')
      
      if (result.success && result.data) {
        const audioResult: AudioExtractionResult = {
          audioData: result.data,
          filename: `${videoFile.name.split('.')[0]}_audio.mp3`,
          duration: 0, // TODO: 获取实际时长
          size: result.data.length
        }
        
        setAudioResult(audioResult)
        
        setProcessingState({
          isLoading: false,
          isProcessing: false,
          progress: 100,
          currentStep: '音频提取完成'
        })

        toast.success('音频提取完成！')
      } else {
        throw new Error(result.error || '音频提取失败')
      }
    } catch (error) {
      console.error('音频提取失败:', error)
      setProcessingState({
        isLoading: false,
        isProcessing: false,
        progress: 0,
        currentStep: '',
        error: error instanceof Error ? error.message : '未知错误'
      })

      // 根据错误类型提供更具体的提示
      const errorMessage = error instanceof Error ? error.message : '未知错误'
      if (errorMessage.includes('FFmpeg')) {
        toast.error(`FFmpeg 相关错误: ${errorMessage}`)
      } else {
        toast.error(`音频提取失败: ${errorMessage}`)
      }
    }
  }, [ffmpeg, ensureFFmpegReady])

  // 处理文件上传
  const handleFilesUploaded = useCallback((uploadedFiles: VideoFileInfo[]) => {
    setFiles(uploadedFiles)
    setAudioResult(null)

    // 单文件模式自动开始处理
    if (!modeConfig.supportMultipleFiles && uploadedFiles.length === 1) {
      // 延迟执行，确保状态更新完成
      setTimeout(async () => {
        try {
          toast.loading('正在准备处理视频...')

          // 使用统一的 FFmpeg 就绪检查
          await ensureFFmpegReady()

          toast.dismiss()
          toast.success('FFmpeg 准备完成')

          // 开始提取音频
          toast.loading('开始提取音频...')
          await extractAudio(uploadedFiles[0])
          toast.dismiss()

        } catch (error) {
          console.error('自动处理失败:', error)
          toast.dismiss()

          const errorMessage = error instanceof Error ? error.message : '未知错误'

          // 根据错误类型提供更具体的提示
          if (errorMessage.includes('FFmpeg')) {
            toast.error(`FFmpeg 相关错误: ${errorMessage}`)
          } else if (errorMessage.includes('not supported')) {
            toast.error('不支持的文件格式，请选择 MP4、WebM、AVI、MOV 或 MKV 格式')
          } else {
            toast.error(`自动处理失败: ${errorMessage}，请手动重试`)
          }
        }
      }, 1000) // 减少延迟时间，因为现在有更好的状态管理
    }
  }, [modeConfig, extractAudio, ensureFFmpegReady])

  // 合并视频（短剧模式）
  const handleMergeVideos = useCallback(async () => {
    if (files.length < 2) {
      toast.error('至少需要2个视频文件才能合并')
      return
    }

    try {
      // 确保 FFmpeg 就绪
      await ensureFFmpegReady()
    } catch (error) {
      toast.error('FFmpeg 未就绪，请稍后重试')
      return
    }

    setIsMerging(true)
    
    try {
      setProcessingState({
        isLoading: false,
        isProcessing: true,
        progress: 0,
        currentStep: '正在合并视频...'
      })

      // TODO: 实现视频合并逻辑
      // 这里需要使用 FFmpeg 的 concat 功能
      
      // 模拟合并过程
      for (let i = 0; i <= 100; i += 10) {
        await new Promise(resolve => setTimeout(resolve, 200))
        setProcessingState(prev => ({
          ...prev,
          progress: i,
          currentStep: `正在合并视频... ${i}%`
        }))
      }

      // 合并完成后提取音频
      // 这里应该使用合并后的视频文件
      const firstFile = files[0]
      await extractAudio(firstFile)

    } catch (error) {
      console.error('视频合并失败:', error)
      toast.error(`视频合并失败: ${error instanceof Error ? error.message : '未知错误'}`)
    } finally {
      setIsMerging(false)
    }
  }, [files, extractAudio, ensureFFmpegReady])

  // 下载音频
  const downloadAudio = useCallback(() => {
    if (!audioResult) return

    const blob = new Blob([audioResult.audioData], { type: 'audio/mp3' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = audioResult.filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    toast.success('音频文件下载开始')
  }, [audioResult])

  // 重新开始
  const handleRestart = useCallback(() => {
    setFiles([])
    setAudioResult(null)
    setProcessingState({
      isLoading: false,
      isProcessing: false,
      progress: 0,
      currentStep: ''
    })
    setIsMerging(false)
  }, [])

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 主要内容区域 */}
      <div className="space-y-6">
        {/* 视频上传 */}
        <VideoUploader
          mode={mode}
          onFilesUploaded={handleFilesUploaded}
          onProcessingStateChange={setProcessingState}
        />

        {/* 短剧模式的视频排序 */}
        {modeConfig.supportMultipleFiles && files.length > 0 && (
          <VideoSorter
            files={files}
            onFilesReorder={setFiles}
            onMergeRequest={handleMergeVideos}
            isMerging={isMerging}
          />
        )}

        {/* 处理状态 */}
        <AnimatePresence>
          {(processingState.isLoading || processingState.isProcessing) && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <Card>
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <Icon type="loader" size="sm" className="animate-spin text-blue-500" />
                      <span className="font-medium">{processingState.currentStep}</span>
                    </div>
                    
                    {processingState.progress > 0 && (
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>处理进度</span>
                          <span>{processingState.progress.toFixed(1)}%</span>
                        </div>
                        <Progress value={processingState.progress} />
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>

        {/* 音频提取结果 */}
        <AnimatePresence>
          {audioResult && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <Card>
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-green-100 dark:bg-green-900/50 rounded-full flex items-center justify-center">
                        <Icon type="check" size="sm" className="text-green-600 dark:text-green-400" />
                      </div>
                      <div>
                        <h3 className="font-medium">音频提取完成</h3>
                        <p className="text-sm text-gray-500">
                          文件大小: {formatFileSize(audioResult.size)}
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex gap-3">
                      <Button
                        onClick={downloadAudio}
                        className="flex items-center gap-2"
                        style={{
                          backgroundColor: '#4f25b8',
                          color: '#ffffff',
                          border: 'none'
                        }}
                      >
                        <Icon type="download" size="xs" />
                        下载音频文件
                      </Button>
                      
                      <Button
                        variant="outline"
                        onClick={handleRestart}
                        style={{
                          borderColor: '#4f25b8',
                          color: '#ffffff',
                          backgroundColor: 'transparent'
                        }}
                      >
                        <Icon type="refresh" size="xs" className="mr-2" />
                        重新开始
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>

        {/* 错误状态 */}
        <AnimatePresence>
          {processingState.error && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <Card className="border-red-200 dark:border-red-800">
                <CardContent className="p-6">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 bg-red-100 dark:bg-red-900/50 rounded-full flex items-center justify-center">
                      <Icon type="x" size="sm" className="text-red-600 dark:text-red-400" />
                    </div>
                    <div>
                      <h3 className="font-medium text-red-600 dark:text-red-400">处理失败</h3>
                      <p className="text-sm text-red-500 dark:text-red-400">
                        {processingState.error}
                      </p>
                    </div>
                  </div>
                  
                  <div className="mt-4">
                    <Button variant="outline" onClick={handleRestart}>
                      <Icon type="refresh" size="xs" className="mr-2" />
                      重新开始
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  )
}
