'use client'

import { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import Icon from '@/components/ui/icon'
import { motion } from 'framer-motion'
import { NARRATION_MODES, type NarrationMode } from '@/types/narration'

interface NarrationModeSelectorProps {
  selectedMode: NarrationMode
  onModeChange: (mode: NarrationMode) => void
  className?: string
}

export default function NarrationModeSelector({
  selectedMode,
  onModeChange,
  className
}: NarrationModeSelectorProps) {
  const [hoveredMode, setHoveredMode] = useState<NarrationMode | null>(null)

  return (
    <div className={`space-y-3 ${className}`}>
      <div className="text-xs font-medium uppercase text-primary mb-3">
        解说模式
      </div>
      
      <div className="space-y-2">
        {NARRATION_MODES.map((mode) => (
          <motion.div
            key={mode.id}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onHoverStart={() => setHoveredMode(mode.id)}
            onHoverEnd={() => setHoveredMode(null)}
          >
            <Card
              className={`cursor-pointer transition-all duration-200 ${
                selectedMode === mode.id
                  ? 'border-blue-500 bg-blue-50/50 dark:bg-blue-950/20'
                  : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50/50 dark:hover:bg-gray-800/50'
              }`}
              onClick={() => onModeChange(mode.id)}
            >
              <CardContent className="p-4">
                <div className="flex items-start gap-3">
                  <div className={`p-2 rounded-lg ${
                    selectedMode === mode.id
                      ? 'bg-blue-100 text-blue-600 dark:bg-blue-900/50 dark:text-blue-400'
                      : 'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400'
                  }`}>
                    <Icon type={mode.icon as any} size="sm" />
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <h3 className="font-medium text-sm">{mode.name}</h3>
                      <div className="flex gap-1">
                        {mode.supportMultipleFiles && (
                          <Badge variant="outline" className="text-xs">
                            多文件
                          </Badge>
                        )}
                        {mode.supportMerge && (
                          <Badge variant="outline" className="text-xs">
                            合并
                          </Badge>
                        )}
                      </div>
                    </div>
                    
                    <p className="text-xs text-gray-500 dark:text-gray-400 leading-relaxed">
                      {mode.description}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>
      
      {/* 模式说明 */}
      <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
        <div className="text-xs text-gray-600 dark:text-gray-400">
          <div className="font-medium mb-1">当前模式特性：</div>
          <ul className="space-y-1">
            {selectedMode === 'short-drama' ? (
              <>
                <li>• 支持批量上传多个视频文件</li>
                <li>• 可拖拽调整视频播放顺序</li>
                <li>• 一键合并所有视频文件</li>
                <li>• 合并后自动提取音频</li>
              </>
            ) : (
              <>
                <li>• 单视频文件上传</li>
                <li>• 自动加载 FFmpeg 处理引擎</li>
                <li>• 上传完成后自动提取音频</li>
                <li>• 支持多种视频格式</li>
              </>
            )}
          </ul>
        </div>
      </div>
    </div>
  )
}
