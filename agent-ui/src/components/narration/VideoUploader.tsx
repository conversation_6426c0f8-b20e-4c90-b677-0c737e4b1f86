'use client'

import { useCallback, useRef, useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import Icon from '@/components/ui/icon'
import { motion, AnimatePresence } from 'framer-motion'
import { toast } from 'sonner'
import { 
  type NarrationMode, 
  type VideoFileInfo, 
  type ProcessingState,
  formatFileSize,
  generateId,
  getNarrationModeConfig
} from '@/types/narration'

interface VideoUploaderProps {
  mode: NarrationMode
  onFilesUploaded: (files: VideoFileInfo[]) => void
  onProcessingStateChange: (state: ProcessingState) => void
  className?: string
}

export default function VideoUploader({
  mode,
  onFilesUploaded,
  onProcessingStateChange: _onProcessingStateChange, // eslint-disable-line @typescript-eslint/no-unused-vars
  className
}: VideoUploaderProps) {
  const [isDragOver, setIsDragOver] = useState(false)
  const [_dragDepth, setDragDepth] = useState(0) // eslint-disable-line @typescript-eslint/no-unused-vars
  const [uploadedFiles, setUploadedFiles] = useState<VideoFileInfo[]>([])
  const [processingState, _setProcessingState] = useState<ProcessingState>({ // eslint-disable-line @typescript-eslint/no-unused-vars
    isLoading: false,
    isProcessing: false,
    progress: 0,
    currentStep: ''
  })

  const fileInputRef = useRef<HTMLInputElement>(null)
  const modeConfig = getNarrationModeConfig(mode)

  // 更新处理状态（保留以备将来使用）
  // const updateProcessingState = useCallback((state: Partial<ProcessingState>) => {
  //   const newState = { ...processingState, ...state }
  //   setProcessingState(newState)
  //   onProcessingStateChange(newState)
  // }, [processingState, onProcessingStateChange])

  // 验证文件类型
  const validateFile = useCallback((file: File): boolean => {
    const validTypes = ['video/mp4', 'video/webm', 'video/avi', 'video/mov', 'video/mkv']
    if (!validTypes.includes(file.type)) {
      toast.error(`不支持的文件格式: ${file.type}`)
      return false
    }
    
    // 文件大小限制 (500MB)
    const maxSize = 500 * 1024 * 1024
    if (file.size > maxSize) {
      toast.error('文件大小不能超过 500MB')
      return false
    }
    
    return true
  }, [])

  // 处理文件选择
  const handleFiles = useCallback((files: FileList) => {
    const fileArray = Array.from(files)
    
    // 单文件模式检查
    if (!modeConfig.supportMultipleFiles && fileArray.length > 1) {
      toast.error(`${modeConfig.name}模式只支持单个文件上传`)
      return
    }
    
    // 验证所有文件
    const validFiles = fileArray.filter(validateFile)
    if (validFiles.length === 0) {
      return
    }
    
    // 创建文件信息
    const videoFiles: VideoFileInfo[] = validFiles.map((file, index) => ({
      id: generateId(),
      file,
      name: file.name,
      size: file.size,
      order: uploadedFiles.length + index + 1,
      status: 'pending'
    }))
    
    const newFiles = [...uploadedFiles, ...videoFiles]
    setUploadedFiles(newFiles)
    onFilesUploaded(newFiles)
    
    toast.success(`成功添加 ${validFiles.length} 个视频文件`)
  }, [modeConfig, uploadedFiles, validateFile, onFilesUploaded])

  // 增强的拖拽处理
  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
    setDragDepth(0)

    const files = e.dataTransfer.files
    if (files.length > 0) {
      handleFiles(files)
    }
  }, [handleFiles])

  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setDragDepth(prev => prev + 1)
    setIsDragOver(true)
  }, [])

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    // 保持拖拽状态
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setDragDepth(prev => {
      const newDepth = prev - 1
      if (newDepth <= 0) {
        setIsDragOver(false)
        return 0
      }
      return newDepth
    })
  }, [])

  // 文件选择
  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files) {
      handleFiles(files)
    }
  }, [handleFiles])

  // 移除文件
  const removeFile = useCallback((fileId: string) => {
    const newFiles = uploadedFiles.filter(f => f.id !== fileId)
    setUploadedFiles(newFiles)
    onFilesUploaded(newFiles)
    toast.success('文件已移除')
  }, [uploadedFiles, onFilesUploaded])

  // 清空所有文件
  const clearAllFiles = useCallback(() => {
    setUploadedFiles([])
    onFilesUploaded([])
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
    toast.success('已清空所有文件')
  }, [onFilesUploaded])

  return (
    <div className={`relative space-y-4 ${className}`}>
      {/* 全屏拖拽覆盖层 */}
      <AnimatePresence>
        {isDragOver && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm flex items-center justify-center"
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
          >
            <motion.div
              initial={{ scale: 0.8, y: 20 }}
              animate={{ scale: 1, y: 0 }}
              exit={{ scale: 0.8, y: 20 }}
              className="text-center space-y-6 p-8"
            >
              <motion.div
                className="w-32 h-32 mx-auto rounded-full bg-gradient-to-br from-brand to-blue-500 flex items-center justify-center relative"
                animate={{
                  scale: [1, 1.1, 1],
                  rotate: [0, 10, -10, 0]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              >
                <Icon type="upload" size="lg" className="text-white" />

                {/* 外圈动画 */}
                <div className="absolute inset-0 rounded-full border-4 border-brand/30 animate-ping" />
                <div className="absolute inset-0 rounded-full border-2 border-brand/50" style={{
                  animation: 'spin 3s linear infinite reverse'
                }} />
              </motion.div>

              <div className="space-y-2">
                <motion.h2
                  className="text-3xl font-bold bg-gradient-to-r from-brand to-blue-500 bg-clip-text text-transparent"
                  animate={{ opacity: [0.7, 1, 0.7] }}
                  transition={{ duration: 1.5, repeat: Infinity }}
                >
                  释放以上传文件
                </motion.h2>
                <p className="text-lg text-muted-foreground">
                  支持 MP4、WebM、AVI、MOV、MKV 格式
                </p>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
      {/* 科技感上传区域 */}
      <Card className="relative overflow-hidden">
        <CardContent className="p-0">
          {/* 背景网格和渐变效果 */}
          <div className="absolute inset-0 opacity-30">
            <div className="absolute inset-0 bg-gradient-to-br from-brand/20 via-transparent to-blue-500/20" />
            <div
              className="absolute inset-0 opacity-20"
              style={{
                backgroundImage: `
                  linear-gradient(rgba(79, 37, 184, 0.1) 1px, transparent 1px),
                  linear-gradient(90deg, rgba(79, 37, 184, 0.1) 1px, transparent 1px)
                `,
                backgroundSize: '20px 20px'
              }}
            />
          </div>

          <div
            className={`relative border-2 rounded-lg m-3 sm:m-6 p-4 sm:p-8 text-center transition-all duration-500 cursor-pointer group ${
              isDragOver
                ? 'border-brand bg-brand/10 shadow-[0_0_30px_rgba(79,37,184,0.3)] scale-[1.02]'
                : 'border-gray-600/50 hover:border-brand/70 hover:bg-brand/5 hover:shadow-[0_0_20px_rgba(79,37,184,0.2)]'
            }`}
            onDrop={handleDrop}
            onDragEnter={handleDragEnter}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onClick={() => fileInputRef.current?.click()}
          >
            <input
              ref={fileInputRef}
              type="file"
              accept="video/*"
              multiple={modeConfig.supportMultipleFiles}
              onChange={handleFileSelect}
              className="hidden"
            />

            {/* 发光边框效果 */}
            <div className={`absolute inset-0 rounded-lg transition-opacity duration-500 ${
              isDragOver ? 'opacity-100' : 'opacity-0 group-hover:opacity-50'
            }`}>
              <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-brand via-blue-500 to-brand opacity-20 blur-sm" />
            </div>

            <motion.div
              animate={{
                scale: isDragOver ? 1.05 : 1,
                y: isDragOver ? -5 : 0
              }}
              transition={{
                duration: 0.3,
                type: "spring",
                stiffness: 300,
                damping: 20
              }}
              className="relative space-y-4 sm:space-y-6"
            >
              {/* 3D风格上传图标 */}
              <div className="relative mx-auto w-16 h-16 sm:w-20 sm:h-20">
                <motion.div
                  className={`absolute inset-0 rounded-full transition-all duration-500 ${
                    isDragOver
                      ? 'bg-gradient-to-br from-brand to-blue-500 shadow-[0_0_25px_rgba(79,37,184,0.5)]'
                      : 'bg-gradient-to-br from-gray-700 to-gray-800 group-hover:from-brand/80 group-hover:to-blue-500/80 group-hover:shadow-[0_0_20px_rgba(79,37,184,0.3)]'
                  }`}
                  animate={{
                    rotate: isDragOver ? 360 : 0,
                  }}
                  transition={{ duration: 0.8, ease: "easeInOut" }}
                />
                <div className="absolute inset-2 rounded-full bg-background/90 flex items-center justify-center">
                  <motion.div
                    animate={{
                      scale: isDragOver ? 1.2 : 1,
                      rotate: isDragOver ? 10 : 0,
                    }}
                    transition={{ duration: 0.3 }}
                  >
                    <Icon
                      type="upload"
                      size="lg"
                      color={isDragOver ? 'brand' : 'primary'}
                    />
                  </motion.div>
                </div>

                {/* 粒子效果 */}
                {isDragOver && (
                  <div className="absolute inset-0">
                    {[...Array(6)].map((_, i) => (
                      <motion.div
                        key={i}
                        className="absolute w-1 h-1 bg-brand rounded-full"
                        initial={{
                          x: 40,
                          y: 40,
                          opacity: 0,
                          scale: 0
                        }}
                        animate={{
                          x: 40 + Math.cos(i * 60 * Math.PI / 180) * 30,
                          y: 40 + Math.sin(i * 60 * Math.PI / 180) * 30,
                          opacity: [0, 1, 0],
                          scale: [0, 1, 0]
                        }}
                        transition={{
                          duration: 1.5,
                          repeat: Infinity,
                          delay: i * 0.1
                        }}
                      />
                    ))}
                  </div>
                )}
              </div>

              <div className="space-y-3 sm:space-y-4">
                <motion.h3
                  className={`text-lg sm:text-xl font-semibold transition-all duration-300 ${
                    isDragOver
                      ? 'text-brand bg-gradient-to-r from-brand to-blue-500 bg-clip-text text-transparent'
                      : 'text-primary group-hover:text-brand'
                  }`}
                  animate={{
                    scale: isDragOver ? 1.05 : 1,
                  }}
                  transition={{ duration: 0.3 }}
                >
                  上传视频，开始您的影视解说之旅
                </motion.h3>

                <motion.p
                  className={`text-xs sm:text-sm transition-colors duration-300 px-2 sm:px-0 ${
                    isDragOver
                      ? 'text-brand/80'
                      : 'text-muted-foreground group-hover:text-primary/80'
                  }`}
                  animate={{
                    y: isDragOver ? -2 : 0,
                  }}
                  transition={{ duration: 0.3 }}
                >
                  {modeConfig.supportMultipleFiles
                    ? '拖拽多个视频文件到此处或点击选择文件'
                    : '拖拽视频文件到此处或点击选择文件'
                  }
                </motion.p>

                {/* 科技感文件格式标签 */}
                <motion.div
                  className="flex flex-wrap justify-center gap-2 sm:gap-3"
                  animate={{
                    y: isDragOver ? -3 : 0,
                  }}
                  transition={{ duration: 0.3, delay: 0.1 }}
                >
                  {['MP4', 'WebM', 'AVI', 'MOV', 'MKV'].map((format, index) => (
                    <motion.div
                      key={format}
                      className={`relative px-2 sm:px-3 py-1 sm:py-1.5 rounded-full text-xs font-medium transition-all duration-300 ${
                        isDragOver
                          ? 'bg-brand/20 text-brand border border-brand/50 shadow-[0_0_10px_rgba(79,37,184,0.3)]'
                          : 'bg-gray-800/50 text-gray-300 border border-gray-600/50 group-hover:bg-brand/10 group-hover:text-brand group-hover:border-brand/30'
                      }`}
                      whileHover={{
                        scale: 1.1,
                        y: -2,
                        boxShadow: isDragOver
                          ? '0 0 15px rgba(79,37,184,0.4)'
                          : '0 0 10px rgba(79,37,184,0.2)'
                      }}
                      transition={{ duration: 0.2, delay: index * 0.05 }}
                    >
                      {format}
                      <div className={`absolute inset-0 rounded-full transition-opacity duration-300 ${
                        isDragOver ? 'opacity-100' : 'opacity-0'
                      }`}>
                        <div className="absolute inset-0 rounded-full bg-gradient-to-r from-brand/20 to-blue-500/20 blur-sm" />
                      </div>
                    </motion.div>
                  ))}
                </motion.div>
              </div>
            </motion.div>
          </div>
        </CardContent>
      </Card>

      {/* 科技感文件列表 */}
      <AnimatePresence>
        {uploadedFiles.length > 0 && (
          <motion.div
            initial={{ opacity: 0, height: 0, y: 20 }}
            animate={{ opacity: 1, height: 'auto', y: 0 }}
            exit={{ opacity: 0, height: 0, y: -20 }}
            transition={{ duration: 0.4, ease: "easeOut" }}
          >
            <Card className="relative overflow-hidden border-gray-600/50 bg-background/50 backdrop-blur-sm">
              {/* 背景渐变 */}
              <div className="absolute inset-0 bg-gradient-to-br from-brand/5 via-transparent to-blue-500/5" />

              <CardContent className="relative p-6">
                <div className="flex items-center justify-between mb-6">
                  <motion.h4
                    className="font-semibold text-lg flex items-center gap-2"
                    initial={{ x: -20, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.2 }}
                  >
                    <div className="w-2 h-2 bg-brand rounded-full animate-pulse" />
                    已选择的文件 ({uploadedFiles.length})
                  </motion.h4>

                  <motion.div
                    initial={{ x: 20, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.3 }}
                  >
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={clearAllFiles}
                      className="hover:bg-red-500/20 hover:text-red-400 hover:border-red-500/50 transition-all duration-300"
                    >
                      <Icon type="trash" size="xs" className="mr-2" />
                      清空
                    </Button>
                  </motion.div>
                </div>

                <div className="space-y-3 max-h-48 overflow-y-auto sidebar-scroll">
                  {uploadedFiles.map((file, index) => (
                    <motion.div
                      key={file.id}
                      initial={{ opacity: 0, x: -30, scale: 0.95 }}
                      animate={{ opacity: 1, x: 0, scale: 1 }}
                      exit={{ opacity: 0, x: 30, scale: 0.95 }}
                      transition={{
                        duration: 0.3,
                        delay: index * 0.1,
                        type: "spring",
                        stiffness: 300,
                        damping: 25
                      }}
                      whileHover={{
                        scale: 1.02,
                        y: -2,
                        transition: { duration: 0.2 }
                      }}
                      className="group relative"
                    >
                      <div className="flex items-center justify-between p-4 bg-gray-800/30 border border-gray-600/30 rounded-lg backdrop-blur-sm hover:bg-gray-700/40 hover:border-brand/30 transition-all duration-300">
                        {/* 发光边框效果 */}
                        <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-brand/10 via-transparent to-blue-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                        <div className="relative flex items-center gap-4 flex-1 min-w-0">
                          {/* 文件图标 */}
                          <div className="relative">
                            <motion.div
                              className="w-10 h-10 rounded-lg bg-gradient-to-br from-brand/20 to-blue-500/20 flex items-center justify-center border border-brand/30"
                              whileHover={{
                                scale: 1.1,
                                boxShadow: '0 0 15px rgba(79,37,184,0.3)'
                              }}
                              transition={{ duration: 0.2 }}
                            >
                              <Icon type="video" size="sm" color="brand" className="flex-shrink-0" />
                            </motion.div>

                            {/* 序号指示器 */}
                            {modeConfig.supportMultipleFiles && (
                              <div className="absolute -top-1 -right-1 w-5 h-5 bg-brand rounded-full flex items-center justify-center text-xs font-bold text-white shadow-lg">
                                {file.order}
                              </div>
                            )}
                          </div>

                          <div className="min-w-0 flex-1">
                            <p className="text-sm font-medium truncate text-primary group-hover:text-brand transition-colors duration-300">
                              {file.name}
                            </p>
                            <div className="flex items-center gap-3 text-xs text-muted-foreground">
                              <span className="flex items-center gap-1">
                                <div className="w-1 h-1 bg-brand rounded-full" />
                                {formatFileSize(file.size)}
                              </span>
                              {file.status && (
                                <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${
                                  file.status === 'pending' ? 'bg-yellow-500/20 text-yellow-400' :
                                  file.status === 'processing' ? 'bg-blue-500/20 text-blue-400' :
                                  file.status === 'completed' ? 'bg-green-500/20 text-green-400' :
                                  'bg-red-500/20 text-red-400'
                                }`}>
                                  {file.status === 'pending' ? '待处理' :
                                   file.status === 'processing' ? '处理中' :
                                   file.status === 'completed' ? '已完成' : '错误'}
                                </span>
                              )}
                            </div>
                          </div>
                        </div>

                        <motion.div
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeFile(file.id)}
                            className="relative flex-shrink-0 hover:bg-red-500/20 hover:text-red-400 transition-all duration-300"
                          >
                            <Icon type="x" size="xs" />
                          </Button>
                        </motion.div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 科技感处理进度 */}
      <AnimatePresence>
        {(processingState.isLoading || processingState.isProcessing) && (
          <motion.div
            initial={{ opacity: 0, height: 0, scale: 0.95 }}
            animate={{ opacity: 1, height: 'auto', scale: 1 }}
            exit={{ opacity: 0, height: 0, scale: 0.95 }}
            transition={{ duration: 0.4, ease: "easeOut" }}
          >
            <Card className="relative overflow-hidden border-brand/50 bg-background/50 backdrop-blur-sm">
              {/* 动态背景效果 */}
              <div className="absolute inset-0">
                <div className="absolute inset-0 bg-gradient-to-r from-brand/10 via-blue-500/10 to-brand/10 animate-pulse" />
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-transparent via-brand/20 to-transparent"
                  animate={{
                    x: ['-100%', '100%'],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "linear"
                  }}
                />
              </div>

              <CardContent className="relative p-6">
                <div className="space-y-4">
                  <motion.div
                    className="flex items-center gap-3"
                    initial={{ x: -20, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.2 }}
                  >
                    <div className="relative">
                      <motion.div
                        className="w-8 h-8 rounded-full bg-gradient-to-r from-brand to-blue-500 flex items-center justify-center"
                        animate={{ rotate: 360 }}
                        transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                      >
                        <Icon type="loader" size="sm" className="text-white" />
                      </motion.div>

                      {/* 外圈发光效果 */}
                      <div className="absolute inset-0 rounded-full bg-brand/30 animate-ping" />
                    </div>

                    <div className="flex-1">
                      <motion.span
                        className="font-semibold text-brand text-lg"
                        animate={{ opacity: [0.7, 1, 0.7] }}
                        transition={{ duration: 1.5, repeat: Infinity }}
                      >
                        {processingState.currentStep}
                      </motion.span>
                    </div>
                  </motion.div>

                  {processingState.progress > 0 && (
                    <motion.div
                      className="space-y-3"
                      initial={{ y: 20, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ delay: 0.3 }}
                    >
                      <div className="flex justify-between items-center text-sm">
                        <span className="text-muted-foreground">处理进度</span>
                        <motion.span
                          className="font-mono font-bold text-brand"
                          key={processingState.progress}
                          initial={{ scale: 1.2 }}
                          animate={{ scale: 1 }}
                          transition={{ duration: 0.2 }}
                        >
                          {processingState.progress.toFixed(1)}%
                        </motion.span>
                      </div>

                      {/* 科技感进度条 */}
                      <div className="relative h-3 bg-gray-800/50 rounded-full overflow-hidden border border-gray-600/50">
                        <motion.div
                          className="absolute inset-0 bg-gradient-to-r from-brand via-blue-500 to-brand rounded-full"
                          initial={{ width: 0 }}
                          animate={{ width: `${processingState.progress}%` }}
                          transition={{ duration: 0.5, ease: "easeOut" }}
                        />

                        {/* 进度条发光效果 */}
                        <motion.div
                          className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent rounded-full"
                          animate={{
                            x: ['-100%', '100%'],
                          }}
                          transition={{
                            duration: 1.5,
                            repeat: Infinity,
                            ease: "linear"
                          }}
                          style={{ width: `${processingState.progress}%` }}
                        />

                        {/* 进度条边缘高光 */}
                        <div
                          className="absolute top-0 right-0 w-1 h-full bg-white/50 rounded-r-full transition-all duration-500"
                          style={{
                            transform: `translateX(${100 - processingState.progress}%)`,
                            opacity: processingState.progress > 5 ? 1 : 0
                          }}
                        />
                      </div>
                    </motion.div>
                  )}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
