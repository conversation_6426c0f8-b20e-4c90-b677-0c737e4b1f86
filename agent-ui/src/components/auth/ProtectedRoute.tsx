'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuthContext } from './AuthProvider'

interface ProtectedRouteProps {
  children: React.ReactNode
  fallback?: React.ReactNode
}

export function ProtectedRoute({ children, fallback }: ProtectedRouteProps) {
  const { user, loading, initialized } = useAuthContext()
  const router = useRouter()

  useEffect(() => {
    if (initialized && !loading && !user) {
      router.push('/auth')
    }
  }, [user, loading, initialized, router])

  // Show loading state while checking auth
  if (loading || !initialized) {
    return (
      fallback || (
        <div className="min-h-screen bg-background flex items-center justify-center">
          <div className="w-8 h-8 border-2 border-brand border-t-transparent rounded-full animate-spin" />
        </div>
      )
    )
  }

  // Show nothing while redirecting
  if (!user) {
    return null
  }

  return <>{children}</>
}

export default ProtectedRoute
