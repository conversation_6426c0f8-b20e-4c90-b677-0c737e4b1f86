'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { useAuthContext } from './AuthProvider'
import { useUserProfile } from '@/hooks/useUserProfile'
import { toast } from 'sonner'
import Icon from '@/components/ui/icon'
import Paragraph from '@/components/ui/typography/Paragraph'

export function UserMenu() {
  const [isOpen, setIsOpen] = useState(false)
  const { user, signOut } = useAuthContext()
  const { profile } = useUserProfile()

  const handleSignOut = async () => {
    try {
      await signOut()
      toast.success('已成功退出登录')
    } catch (error: any) {
      toast.error('退出登录失败：' + error.message)
    }
  }

  if (!user) return null

  const displayName = profile?.display_name || user.email?.split('@')[0] || '用户'
  const avatarUrl = profile?.avatar_url

  return (
    <div className="relative">
      <Button
        variant="ghost"
        className="w-full justify-start gap-3 p-3 h-auto bg-primaryAccent/50 hover:bg-primaryAccent border border-accent"
        onClick={() => setIsOpen(!isOpen)}
      >
        {/* Avatar */}
        <div className="w-8 h-8 rounded-full bg-brand/20 border border-brand/30 flex items-center justify-center overflow-hidden">
          {avatarUrl ? (
            <img 
              src={avatarUrl} 
              alt={displayName}
              className="w-full h-full object-cover"
            />
          ) : (
            <span className="text-brand text-sm font-medium">
              {displayName.charAt(0).toUpperCase()}
            </span>
          )}
        </div>

        {/* User Info */}
        <div className="flex-1 text-left">
          <Paragraph size="small" className="text-primary font-medium">
            {displayName}
          </Paragraph>
          <Paragraph size="xsmall" className="text-muted truncate">
            {user.email}
          </Paragraph>
        </div>

        {/* Chevron */}
        <Icon 
          type={isOpen ? 'chevron-up' : 'chevron-down'} 
          size="xs" 
          className="text-muted"
        />
      </Button>

      {/* Dropdown Menu */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="absolute bottom-full left-0 right-0 mb-2 bg-primaryAccent border border-accent rounded-xl shadow-lg overflow-hidden z-50"
            initial={{ opacity: 0, y: 10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
          >
            <div className="p-2 space-y-1">
              <Button
                variant="ghost"
                className="w-full justify-start gap-3 text-primary hover:bg-accent"
                onClick={() => {
                  setIsOpen(false)
                  // TODO: Open profile settings
                  toast.info('个人资料设置功能即将推出')
                }}
              >
                <div className="w-4 h-4 rounded-full bg-muted/30 flex items-center justify-center">
                  <svg className="w-3 h-3 text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                </div>
                个人资料
              </Button>

              <Button
                variant="ghost"
                className="w-full justify-start gap-3 text-primary hover:bg-accent"
                onClick={() => {
                  setIsOpen(false)
                  // TODO: Open settings
                  toast.info('设置功能即将推出')
                }}
              >
                <div className="w-4 h-4 rounded-full bg-muted/30 flex items-center justify-center">
                  <svg className="w-3 h-3 text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                </div>
                设置
              </Button>

              <div className="h-px bg-accent my-1" />

              <Button
                variant="ghost"
                className="w-full justify-start gap-3 text-destructive hover:bg-destructive/10"
                onClick={() => {
                  setIsOpen(false)
                  handleSignOut()
                }}
              >
                <div className="w-4 h-4 rounded-full bg-destructive/20 flex items-center justify-center">
                  <svg className="w-3 h-3 text-destructive" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                  </svg>
                </div>
                退出登录
              </Button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Backdrop */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  )
}
