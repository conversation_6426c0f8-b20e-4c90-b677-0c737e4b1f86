'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useAuthContext } from './AuthProvider'
import { toast } from 'sonner'
import Icon from '@/components/ui/icon'
import Heading from '@/components/ui/typography/Heading'
import Paragraph from '@/components/ui/typography/Paragraph'

interface AuthFormProps {
  mode: 'signin' | 'signup'
  onModeChange: (mode: 'signin' | 'signup') => void
}

export function AuthForm({ mode, onModeChange }: AuthFormProps) {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [displayName, setDisplayName] = useState('')
  const [loading, setLoading] = useState(false)
  
  const { signInWithEmail, signUpWithEmail, signInWithProvider } = useAuthContext()

  const handleEmailAuth = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!email || !password) {
      toast.error('请填写所有必填字段')
      return
    }

    if (mode === 'signup' && password !== confirmPassword) {
      toast.error('密码不匹配')
      return
    }

    if (password.length < 6) {
      toast.error('密码至少需要6个字符')
      return
    }

    setLoading(true)

    try {
      if (mode === 'signin') {
        await signInWithEmail(email, password)
        toast.success('登录成功！')
      } else {
        await signUpWithEmail(email, password, { display_name: displayName })
        toast.success('注册成功！请检查您的邮箱以验证账户。')
      }
    } catch (error: any) {
      console.error('Auth error:', error)
      toast.error(error.message || '认证失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  const handleProviderAuth = async (provider: 'google' | 'github') => {
    try {
      await signInWithProvider(provider)
    } catch (error: any) {
      console.error('Provider auth error:', error)
      toast.error(error.message || `${provider} 登录失败`)
    }
  }

  return (
    <motion.div
      className="w-full max-w-md space-y-6 rounded-xl bg-background/90 p-8 backdrop-blur-sm border border-border"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Header */}
      <div className="text-center space-y-2">
        <Heading size={2} className="text-primary">
          {mode === 'signin' ? '欢迎回来' : '创建账户'}
        </Heading>
        <Paragraph size="body" className="text-muted">
          {mode === 'signin' 
            ? '登录到您的 NarrAgent 账户' 
            : '开始使用 NarrAgent 智能代理平台'
          }
        </Paragraph>
      </div>

      {/* Social Login */}
      <div className="space-y-3">
        <Button
          type="button"
          variant="outline"
          className="w-full bg-background/50 border-border hover:bg-accent"
          onClick={() => handleProviderAuth('google')}
        >
          <svg className="w-4 h-4 mr-2" viewBox="0 0 24 24">
            <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
            <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
            <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
            <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
          </svg>
          使用 Google 继续
        </Button>
        
        <Button
          type="button"
          variant="outline"
          className="w-full bg-background/50 border-border hover:bg-accent"
          onClick={() => handleProviderAuth('github')}
        >
          <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
          </svg>
          使用 GitHub 继续
        </Button>
      </div>

      {/* Divider */}
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t border-border" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-background px-2 text-muted">或</span>
        </div>
      </div>

      {/* Email Form */}
      <form onSubmit={handleEmailAuth} className="space-y-4">
        {mode === 'signup' && (
          <div className="space-y-2">
            <Label htmlFor="displayName" className="text-primary">显示名称</Label>
            <Input
              id="displayName"
              type="text"
              placeholder="您的显示名称"
              value={displayName}
              onChange={(e) => setDisplayName(e.target.value)}
              className="bg-primaryAccent border-accent text-primary placeholder:text-muted focus:border-brand"
            />
          </div>
        )}
        
        <div className="space-y-2">
          <Label htmlFor="email" className="text-primary">邮箱</Label>
          <Input
            id="email"
            type="email"
            placeholder="<EMAIL>"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            className="bg-primaryAccent border-accent text-primary placeholder:text-muted focus:border-brand"
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="password" className="text-primary">密码</Label>
          <Input
            id="password"
            type="password"
            placeholder="••••••••"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
            className="bg-primaryAccent border-accent text-primary placeholder:text-muted focus:border-brand"
          />
        </div>

        {mode === 'signup' && (
          <div className="space-y-2">
            <Label htmlFor="confirmPassword" className="text-primary">确认密码</Label>
            <Input
              id="confirmPassword"
              type="password"
              placeholder="••••••••"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              required
              className="bg-primaryAccent border-accent text-primary placeholder:text-muted focus:border-brand"
            />
          </div>
        )}

        <Button
          type="submit"
          className="w-full bg-brand hover:bg-brand/90 text-white"
          disabled={loading}
        >
          {loading ? (
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
              {mode === 'signin' ? '登录中...' : '注册中...'}
            </div>
          ) : (
            mode === 'signin' ? '登录' : '创建账户'
          )}
        </Button>
      </form>

      {/* Switch Mode */}
      <div className="text-center">
        <Paragraph size="xsmall" className="text-muted">
          {mode === 'signin' ? '还没有账户？' : '已有账户？'}
          <button
            type="button"
            onClick={() => onModeChange(mode === 'signin' ? 'signup' : 'signin')}
            className="ml-1 text-brand hover:text-brand/80 underline"
          >
            {mode === 'signin' ? '立即注册' : '立即登录'}
          </button>
        </Paragraph>
      </div>
    </motion.div>
  )
}
