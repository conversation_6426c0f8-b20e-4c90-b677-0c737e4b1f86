'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { useSupabaseStore } from '@/hooks/useSupabaseStore'
import { useAuthContext } from '@/components/auth/AuthProvider'
import Icon from '@/components/ui/icon'
import Paragraph from '@/components/ui/typography/Paragraph'
import { truncateText } from '@/lib/utils'
import { toast } from 'sonner'

export function SupabaseSessions() {
  const { user } = useAuthContext()
  const { 
    sessions, 
    currentSession, 
    switchSession, 
    deleteSession, 
    updateSessionTitle 
  } = useSupabaseStore()
  const [editingSessionId, setEditingSessionId] = useState<string | null>(null)
  const [editTitle, setEditTitle] = useState('')

  const handleEditTitle = (session: any) => {
    setEditingSessionId(session.id)
    setEditTitle(session.title || '')
  }

  const handleSaveTitle = async () => {
    if (editingSessionId && editTitle.trim()) {
      await updateSessionTitle(editingSessionId, editTitle.trim())
      setEditingSessionId(null)
      setEditTitle('')
    }
  }

  const handleCancelEdit = () => {
    setEditingSessionId(null)
    setEditTitle('')
  }

  const handleDeleteSession = async (sessionId: string, e: React.MouseEvent) => {
    e.stopPropagation()
    
    if (confirm('确定要删除这个会话吗？')) {
      await deleteSession(sessionId)
    }
  }

  if (!user) {
    return null
  }

  if (sessions.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-8 text-center">
        <div className="w-12 h-12 rounded-full bg-accent/50 flex items-center justify-center mb-3">
          <Icon type="agent" size="sm" className="text-muted" />
        </div>
        <Paragraph size="small" className="text-muted">
          暂无会话记录
        </Paragraph>
        <Paragraph size="xsmall" className="text-muted mt-1">
          选择一个代理开始对话
        </Paragraph>
      </div>
    )
  }

  return (
    <div className="space-y-2">
      <div className="text-xs font-medium uppercase text-primary">
        会话历史
      </div>
      
      <div className="space-y-1 max-h-64 overflow-y-auto">
        <AnimatePresence>
          {sessions.map((session) => (
            <motion.div
              key={session.id}
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
              className={`group relative rounded-xl border p-3 cursor-pointer transition-all ${
                currentSession?.id === session.id
                  ? 'border-brand bg-brand/10'
                  : 'border-accent bg-primaryAccent hover:bg-accent'
              }`}
              onClick={() => switchSession(session)}
            >
              {editingSessionId === session.id ? (
                <div className="space-y-2" onClick={(e) => e.stopPropagation()}>
                  <input
                    type="text"
                    value={editTitle}
                    onChange={(e) => setEditTitle(e.target.value)}
                    className="w-full bg-background border border-accent rounded px-2 py-1 text-xs text-primary focus:outline-none focus:border-brand"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        handleSaveTitle()
                      } else if (e.key === 'Escape') {
                        handleCancelEdit()
                      }
                    }}
                    autoFocus
                  />
                  <div className="flex gap-1">
                    <Button
                      size="sm"
                      className="h-6 px-2 text-xs bg-brand hover:bg-brand/80"
                      onClick={handleSaveTitle}
                    >
                      保存
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      className="h-6 px-2 text-xs"
                      onClick={handleCancelEdit}
                    >
                      取消
                    </Button>
                  </div>
                </div>
              ) : (
                <>
                  <div className="flex items-start justify-between gap-2">
                    <div className="flex-1 min-w-0">
                      <Paragraph size="small" className="text-primary font-medium truncate">
                        {session.title || '新对话'}
                      </Paragraph>
                      <Paragraph size="xsmall" className="text-muted mt-1">
                        {new Date(session.created_at).toLocaleDateString('zh-CN', {
                          month: 'short',
                          day: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </Paragraph>
                    </div>
                    
                    {/* Action buttons */}
                    <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-6 w-6 p-0 hover:bg-accent"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleEditTitle(session)
                        }}
                      >
                        <Icon type="edit" size="xxs" className="text-muted" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-6 w-6 p-0 hover:bg-destructive/20"
                        onClick={(e) => handleDeleteSession(session.id, e)}
                      >
                        <Icon type="trash" size="xxs" className="text-destructive" />
                      </Button>
                    </div>
                  </div>
                  
                  {/* Active indicator */}
                  {currentSession?.id === session.id && (
                    <div className="absolute left-0 top-1/2 -translate-y-1/2 w-1 h-6 bg-brand rounded-r" />
                  )}
                </>
              )}
            </motion.div>
          ))}
        </AnimatePresence>
      </div>
    </div>
  )
}
