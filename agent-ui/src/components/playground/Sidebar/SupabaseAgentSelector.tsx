'use client'

import * as React from 'react'
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem
} from '@/components/ui/select'
import { useSupabaseStore } from '@/hooks/useSupabaseStore'
import { useAuthContext } from '@/components/auth/AuthProvider'
import Icon from '@/components/ui/icon'
import { useEffect, useState } from 'react'
import { Skeleton } from '@/components/ui/skeleton'

export function SupabaseAgentSelector() {
  const { user } = useAuthContext()
  const { agents, loading, createSession, currentSession } = useSupabaseStore()
  const [selectedAgentId, setSelectedAgentId] = useState<string>('')

  // Handle agent selection
  const handleAgentChange = async (agentId: string) => {
    setSelectedAgentId(agentId)
    
    if (agentId && user) {
      // Create a new session with the selected agent
      await createSession(agentId)
    }
  }

  // Update selected agent when current session changes
  useEffect(() => {
    if (currentSession) {
      setSelectedAgentId(currentSession.agent_id)
    } else {
      setSelectedAgentId('')
    }
  }, [currentSession])

  if (!user) {
    return (
      <div className="flex h-9 w-full items-center justify-center rounded-xl border border-accent bg-primaryAccent text-xs text-muted">
        请先登录
      </div>
    )
  }

  if (loading) {
    return <Skeleton className="h-9 w-full rounded-xl" />
  }

  if (agents.length === 0) {
    return (
      <div className="flex h-9 w-full items-center justify-center rounded-xl border border-accent bg-primaryAccent text-xs text-muted">
        暂无可用代理
      </div>
    )
  }

  return (
    <Select value={selectedAgentId} onValueChange={handleAgentChange}>
      <SelectTrigger className="h-9 w-full rounded-xl border border-accent bg-primaryAccent text-xs font-medium text-primary hover:bg-accent focus:ring-0 focus:ring-offset-0">
        <div className="flex items-center gap-2">
          <Icon type="agent" size="xs" className="text-brand" />
          <SelectValue placeholder="选择代理" />
        </div>
      </SelectTrigger>
      <SelectContent className="bg-primaryAccent border-accent">
        {agents.map((agent) => (
          <SelectItem
            key={agent.id}
            value={agent.id}
            className="text-primary hover:bg-accent focus:bg-accent"
          >
            <div className="flex items-center gap-2">
              <Icon type="agent" size="xs" className="text-brand" />
              <div className="flex flex-col items-start">
                <span className="font-medium">{agent.name}</span>
                {agent.description && (
                  <span className="text-xs text-muted truncate max-w-48">
                    {agent.description}
                  </span>
                )}
              </div>
            </div>
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  )
}
