'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { usePlaygroundStore } from '@/store'
import NarrationWorkspace from '@/components/narration/NarrationWorkspace'
import { type NarrationMode } from '@/types/narration'

// 影视解说主页组件

const ChatBlankState = () => {
  const { selectedEndpoint, selectedNarrationMode } = usePlaygroundStore()

  return (
    <motion.div
      className="flex h-full w-full flex-col px-4 py-6 overflow-y-auto"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, ease: 'easeOut' }}
    >
      <div className="max-w-4xl mx-auto w-full">
        <motion.div
          className="text-center mb-8"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2, duration: 0.5 }}
        >
          <h1 className="text-3xl font-bold mb-4">
            影视解说工作台
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-400 mb-2">
            上传视频，开始您的影视解说之旅
          </p>
          <p className="text-sm text-gray-500 dark:text-gray-500">
            支持多种视频格式，自动提取音频，让解说创作更简单
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.5 }}
        >
          <NarrationWorkspace mode={selectedNarrationMode} />
        </motion.div>
      </div>
    </motion.div>
  )
}

export default ChatBlankState
