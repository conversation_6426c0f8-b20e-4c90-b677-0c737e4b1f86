import { FFmpeg } from '@ffmpeg/ffmpeg'
import { fetchFile, toBlobURL } from '@ffmpeg/util'

/**
 * FFmpeg 实例管理器
 * 提供 FFmpeg 的初始化、加载和核心功能封装
 */
export class FFmpegService {
  private ffmpeg: FFmpeg | null = null
  private isLoaded = false
  private isLoading = false

  /**
   * 获取 FFmpeg 实例
   */
  getInstance(): FFmpeg {
    if (!this.ffmpeg) {
      this.ffmpeg = new FFmpeg()
    }
    return this.ffmpeg
  }

  /**
   * 检查是否已加载
   */
  isFFmpegLoaded(): boolean {
    return this.isLoaded && this.ffmpeg !== null
  }

  /**
   * 简单检查 FFmpeg 实例状态
   */
  isFFmpegReady(): boolean {
    return this.isLoaded && this.ffmpeg !== null && !this.isLoading
  }

  /**
   * 检查是否正在加载
   */
  isFFmpegLoading(): boolean {
    return this.isLoading
  }

  /**
   * 加载 FFmpeg 核心
   * @param useMultithread 是否使用多线程版本
   * @param onProgress 进度回调
   * @param onLog 日志回调
   */
  async load(
    useMultithread = true,
    onProgress?: (progress: number, time: number) => void,
    onLog?: (message: string) => void
  ): Promise<void> {
    if (this.isLoaded) {
      return
    }

    if (this.isLoading) {
      // 如果正在加载，等待加载完成
      return new Promise<void>((resolve, reject) => {
        const checkLoadState = () => {
          if (this.isLoaded) {
            resolve()
          } else if (this.isLoading) {
            // 继续等待
            setTimeout(checkLoadState, 100)
          } else {
            // 加载失败
            reject(new Error('FFmpeg loading failed'))
          }
        }
        checkLoadState()
      })
    }

    this.isLoading = true

    try {
      const ffmpeg = this.getInstance()

      // 设置事件监听器
      if (onProgress) {
        ffmpeg.on('progress', ({ progress, time }) => {
          onProgress(progress, time)
        })
      }

      if (onLog) {
        ffmpeg.on('log', ({ message }) => {
          onLog(message)
        })
      }

      // 首先尝试多线程版本，失败则回退到单线程
      let loadSuccess = false
      let lastError: Error | null = null

      if (useMultithread) {
        try {
          await this.loadWithConfig(true)
          loadSuccess = true
          onLog?.('FFmpeg 多线程版本加载成功')
        } catch (error) {
          lastError = error as Error
          onLog?.(`FFmpeg 多线程版本加载失败: ${lastError.message}，尝试单线程版本...`)
        }
      }

      // 如果多线程失败或未尝试多线程，尝试单线程版本
      if (!loadSuccess) {
        try {
          await this.loadWithConfig(false)
          loadSuccess = true
          onLog?.('FFmpeg 单线程版本加载成功')
        } catch (error) {
          lastError = error as Error
          onLog?.(`FFmpeg 单线程版本加载失败: ${lastError.message}`)
        }
      }

      if (!loadSuccess) {
        throw lastError || new Error('FFmpeg 加载失败')
      }

      // 简单验证加载状态
      if (!this.ffmpeg) {
        throw new Error('FFmpeg 实例创建失败')
      }

      this.isLoaded = true
      onLog?.('FFmpeg 加载完成并验证成功')
    } catch (error) {
      console.error('Failed to load FFmpeg:', error)
      this.isLoaded = false
      this.ffmpeg = null
      throw error
    } finally {
      this.isLoading = false
    }
  }

  /**
   * 使用指定配置加载 FFmpeg
   */
  private async loadWithConfig(useMultithread: boolean): Promise<void> {
    const ffmpeg = this.getInstance()

    // 根据是否使用多线程选择不同的核心
    const corePackage = useMultithread ? '@ffmpeg/core-mt' : '@ffmpeg/core'
    const baseURL = `https://unpkg.com/${corePackage}@0.12.6/dist/umd`

    const loadConfig: any = {
      coreURL: await toBlobURL(`${baseURL}/ffmpeg-core.js`, 'text/javascript'),
      wasmURL: await toBlobURL(`${baseURL}/ffmpeg-core.wasm`, 'application/wasm'),
    }

    // 多线程版本需要额外的 worker
    if (useMultithread) {
      loadConfig.workerURL = await toBlobURL(
        `${baseURL}/ffmpeg-core.worker.js`,
        'text/javascript'
      )
    }

    await ffmpeg.load(loadConfig)
  }

  /**
   * 验证 FFmpeg 实例是否可用
   */
  private async validateFFmpegInstance(): Promise<boolean> {
    try {
      if (!this.ffmpeg || !this.isLoaded) {
        return false
      }

      // 简化验证逻辑，避免执行可能导致崩溃的命令
      // 只检查实例状态，不执行实际命令
      return true
    } catch (error) {
      console.warn('FFmpeg instance validation failed:', error)
      return false
    }
  }

  /**
   * 终止 FFmpeg 实例
   */
  async terminate(): Promise<void> {
    if (this.ffmpeg && this.isLoaded) {
      await this.ffmpeg.terminate()
      this.isLoaded = false
      this.ffmpeg = null
    }
  }

  /**
   * 写入文件到 FFmpeg 虚拟文件系统
   */
  async writeFile(filename: string, data: File | string | Uint8Array): Promise<void> {
    if (!this.isLoaded) {
      throw new Error('FFmpeg is not loaded')
    }

    const ffmpeg = this.getInstance()
    
    if (typeof data === 'string') {
      // 如果是 URL，使用 fetchFile 获取
      await ffmpeg.writeFile(filename, await fetchFile(data))
    } else if (data instanceof File) {
      // 如果是 File 对象，使用 fetchFile 处理
      await ffmpeg.writeFile(filename, await fetchFile(data))
    } else {
      // 如果是 Uint8Array，直接写入
      await ffmpeg.writeFile(filename, data)
    }
  }

  /**
   * 从 FFmpeg 虚拟文件系统读取文件
   */
  async readFile(filename: string): Promise<Uint8Array> {
    if (!this.isLoaded) {
      throw new Error('FFmpeg is not loaded')
    }

    const ffmpeg = this.getInstance()
    const data = await ffmpeg.readFile(filename)
    return data as Uint8Array
  }

  /**
   * 执行 FFmpeg 命令
   */
  async exec(args: string[], timeout?: number): Promise<void> {
    if (!this.isLoaded) {
      throw new Error('FFmpeg is not loaded')
    }

    const ffmpeg = this.getInstance()
    await ffmpeg.exec(args, timeout)
  }

  /**
   * 列出虚拟文件系统中的文件
   */
  async listFiles(path = '/'): Promise<string[]> {
    if (!this.isLoaded) {
      throw new Error('FFmpeg is not loaded')
    }

    // FFmpeg.wasm 可能没有 listDir 方法，这里提供一个占位实现
    // 在实际使用中，可以通过其他方式管理文件列表
    return []
  }

  /**
   * 删除虚拟文件系统中的文件
   */
  async deleteFile(filename: string): Promise<void> {
    if (!this.isLoaded) {
      throw new Error('FFmpeg is not loaded')
    }

    const ffmpeg = this.getInstance()
    await ffmpeg.deleteFile(filename)
  }
}

/**
 * 全局 FFmpeg 服务实例
 */
export const ffmpegService = new FFmpegService()

/**
 * 常用的 FFmpeg 操作封装
 */
export class FFmpegOperations {
  constructor(private service: FFmpegService) {}

  /**
   * 视频格式转换
   */
  async convertVideo(
    inputFile: File,
    outputFormat: string,
    options: {
      quality?: string
      resolution?: string
      bitrate?: string
    } = {}
  ): Promise<Uint8Array> {
    const inputFilename = inputFile.name
    const outputFilename = `output.${outputFormat}`

    // 写入输入文件
    await this.service.writeFile(inputFilename, inputFile)

    // 构建 FFmpeg 命令
    const args = ['-i', inputFilename]

    if (options.quality) {
      args.push('-crf', options.quality)
    }
    if (options.resolution) {
      args.push('-s', options.resolution)
    }
    if (options.bitrate) {
      args.push('-b:v', options.bitrate)
    }

    args.push(outputFilename)

    // 执行转换
    await this.service.exec(args)

    // 读取输出文件
    const outputData = await this.service.readFile(outputFilename)

    // 清理文件
    await this.service.deleteFile(inputFilename)
    await this.service.deleteFile(outputFilename)

    return outputData
  }

  /**
   * 音频格式转换
   */
  async convertAudio(
    inputFile: File,
    outputFormat: string,
    options: {
      bitrate?: string
      sampleRate?: string
    } = {}
  ): Promise<Uint8Array> {
    const inputFilename = inputFile.name
    const outputFilename = `output.${outputFormat}`

    await this.service.writeFile(inputFilename, inputFile)

    const args = ['-i', inputFilename]

    if (options.bitrate) {
      args.push('-b:a', options.bitrate)
    }
    if (options.sampleRate) {
      args.push('-ar', options.sampleRate)
    }

    args.push(outputFilename)

    await this.service.exec(args)
    const outputData = await this.service.readFile(outputFilename)

    await this.service.deleteFile(inputFilename)
    await this.service.deleteFile(outputFilename)

    return outputData
  }

  /**
   * 视频剪辑
   */
  async trimVideo(
    inputFile: File,
    startTime: string,
    duration: string
  ): Promise<Uint8Array> {
    const inputFilename = inputFile.name
    const outputFilename = 'trimmed_output.mp4'

    await this.service.writeFile(inputFilename, inputFile)

    const args = [
      '-i', inputFilename,
      '-ss', startTime,
      '-t', duration,
      '-c', 'copy',
      outputFilename
    ]

    await this.service.exec(args)
    const outputData = await this.service.readFile(outputFilename)

    await this.service.deleteFile(inputFilename)
    await this.service.deleteFile(outputFilename)

    return outputData
  }

  /**
   * 从视频提取音频
   */
  async extractAudio(inputFile: File, outputFormat = 'mp3'): Promise<Uint8Array> {
    const inputFilename = inputFile.name
    const outputFilename = `extracted_audio.${outputFormat}`

    await this.service.writeFile(inputFilename, inputFile)

    const args = [
      '-i', inputFilename,
      '-vn', // 不包含视频
      '-acodec', outputFormat === 'mp3' ? 'libmp3lame' : 'copy',
      outputFilename
    ]

    await this.service.exec(args)
    const outputData = await this.service.readFile(outputFilename)

    await this.service.deleteFile(inputFilename)
    await this.service.deleteFile(outputFilename)

    return outputData
  }
}

/**
 * 全局 FFmpeg 操作实例
 */
export const ffmpegOperations = new FFmpegOperations(ffmpegService)

/**
 * 工具函数：创建下载链接
 */
export function createDownloadUrl(data: Uint8Array, mimeType: string): string {
  const blob = new Blob([data], { type: mimeType })
  return URL.createObjectURL(blob)
}

/**
 * 工具函数：触发文件下载
 */
export function downloadFile(data: Uint8Array, filename: string, mimeType: string): void {
  const url = createDownloadUrl(data, mimeType)
  const a = document.createElement('a')
  a.href = url
  a.download = filename
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}
