import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
})

// Database types for TypeScript
export type Database = {
  public: {
    Tables: {
      user_profiles: {
        Row: {
          id: string
          display_name: string | null
          avatar_url: string | null
          preferences: any
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          display_name?: string | null
          avatar_url?: string | null
          preferences?: any
        }
        Update: {
          display_name?: string | null
          avatar_url?: string | null
          preferences?: any
        }
      }
      organizations: {
        Row: {
          id: string
          name: string
          slug: string
          description: string | null
          settings: any
          created_at: string
          updated_at: string
        }
        Insert: {
          name: string
          slug: string
          description?: string | null
          settings?: any
        }
        Update: {
          name?: string
          slug?: string
          description?: string | null
          settings?: any
        }
      }
      organization_members: {
        Row: {
          id: string
          organization_id: string
          user_id: string
          role: 'owner' | 'admin' | 'member'
          permissions: any
          invited_by: string | null
          joined_at: string
          created_at: string
        }
        Insert: {
          organization_id: string
          user_id: string
          role?: 'owner' | 'admin' | 'member'
          permissions?: any
          invited_by?: string | null
        }
        Update: {
          role?: 'owner' | 'admin' | 'member'
          permissions?: any
        }
      }
      agents: {
        Row: {
          id: string
          organization_id: string
          name: string
          description: string | null
          model_config: any
          system_prompt: string | null
          tools: any
          is_active: boolean
          is_public: boolean
          created_by: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          organization_id: string
          name: string
          description?: string | null
          model_config: any
          system_prompt?: string | null
          tools?: any
          is_active?: boolean
          is_public?: boolean
          created_by?: string | null
        }
        Update: {
          name?: string
          description?: string | null
          model_config?: any
          system_prompt?: string | null
          tools?: any
          is_active?: boolean
          is_public?: boolean
        }
      }
      chat_sessions: {
        Row: {
          id: string
          agent_id: string
          user_id: string
          title: string | null
          metadata: any
          is_archived: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          agent_id: string
          user_id: string
          title?: string | null
          metadata?: any
          is_archived?: boolean
        }
        Update: {
          title?: string | null
          metadata?: any
          is_archived?: boolean
        }
      }
      chat_messages: {
        Row: {
          id: string
          session_id: string
          role: 'user' | 'assistant' | 'system'
          content: string
          metadata: any
          created_at: string
        }
        Insert: {
          session_id: string
          role: 'user' | 'assistant' | 'system'
          content: string
          metadata?: any
        }
        Update: {
          content?: string
          metadata?: any
        }
      }
      knowledge_bases: {
        Row: {
          id: string
          organization_id: string
          name: string
          description: string | null
          settings: any
          is_active: boolean
          created_by: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          organization_id: string
          name: string
          description?: string | null
          settings?: any
          is_active?: boolean
          created_by?: string | null
        }
        Update: {
          name?: string
          description?: string | null
          settings?: any
          is_active?: boolean
        }
      }
      documents: {
        Row: {
          id: string
          knowledge_base_id: string
          title: string
          content: string
          metadata: any
          embedding: number[] | null
          file_path: string | null
          file_size: number | null
          mime_type: string | null
          created_by: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          knowledge_base_id: string
          title: string
          content: string
          metadata?: any
          embedding?: number[] | null
          file_path?: string | null
          file_size?: number | null
          mime_type?: string | null
          created_by?: string | null
        }
        Update: {
          title?: string
          content?: string
          metadata?: any
          embedding?: number[] | null
          file_path?: string | null
          file_size?: number | null
          mime_type?: string | null
        }
      }
      usage_stats: {
        Row: {
          id: string
          organization_id: string
          user_id: string
          resource_type: 'api_call' | 'storage' | 'compute' | 'embedding'
          resource_id: string | null
          quantity: number
          cost_cents: number
          metadata: any
          created_at: string
        }
        Insert: {
          organization_id: string
          user_id: string
          resource_type: 'api_call' | 'storage' | 'compute' | 'embedding'
          resource_id?: string | null
          quantity?: number
          cost_cents?: number
          metadata?: any
        }
        Update: {
          quantity?: number
          cost_cents?: number
          metadata?: any
        }
      }
      api_keys: {
        Row: {
          id: string
          organization_id: string
          name: string
          key_hash: string
          key_prefix: string
          permissions: any
          is_active: boolean
          last_used_at: string | null
          expires_at: string | null
          created_by: string | null
          created_at: string
        }
        Insert: {
          organization_id: string
          name: string
          key_hash: string
          key_prefix: string
          permissions?: any
          is_active?: boolean
          expires_at?: string | null
          created_by?: string | null
        }
        Update: {
          name?: string
          permissions?: any
          is_active?: boolean
          expires_at?: string | null
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      user_role: 'owner' | 'admin' | 'member'
      message_role: 'user' | 'assistant' | 'system'
      resource_type: 'api_call' | 'storage' | 'compute' | 'embedding'
    }
  }
}
