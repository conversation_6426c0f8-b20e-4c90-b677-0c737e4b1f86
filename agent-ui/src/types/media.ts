/**
 * 媒体处理相关的 TypeScript 类型定义
 */

/**
 * 支持的媒体文件类型
 */
export type MediaType = 'video' | 'audio' | 'image'

/**
 * 支持的视频格式
 */
export type VideoFormat = 'mp4' | 'webm' | 'avi' | 'mov' | 'mkv' | 'flv' | 'wmv'

/**
 * 支持的音频格式
 */
export type AudioFormat = 'mp3' | 'wav' | 'aac' | 'ogg' | 'flac' | 'm4a'

/**
 * 支持的图片格式
 */
export type ImageFormat = 'jpg' | 'jpeg' | 'png' | 'webp' | 'gif' | 'bmp'

/**
 * 媒体文件信息
 */
export interface MediaFileInfo {
  name: string
  size: number
  type: MediaType
  format: string
  duration?: number // 音视频时长（秒）
  width?: number // 视频/图片宽度
  height?: number // 视频/图片高度
  bitrate?: number // 比特率
  sampleRate?: number // 音频采样率
  channels?: number // 音频声道数
}

/**
 * FFmpeg 加载状态
 */
export interface FFmpegLoadState {
  isLoaded: boolean
  isLoading: boolean
  error?: string
}

/**
 * 处理进度信息
 */
export interface ProcessingProgress {
  progress: number // 0-1 之间的进度值
  time: number // 已处理时间（微秒）
  speed?: number // 处理速度倍数
  eta?: number // 预计剩余时间（秒）
}

/**
 * 处理状态
 */
export type ProcessingStatus = 'idle' | 'loading' | 'processing' | 'completed' | 'error'

/**
 * 媒体处理任务
 */
export interface MediaProcessingTask {
  id: string
  type: MediaProcessingType
  inputFile: File
  outputFormat: string
  options: ProcessingOptions
  status: ProcessingStatus
  progress?: ProcessingProgress
  error?: string
  result?: Uint8Array
  createdAt: Date
  completedAt?: Date
}

/**
 * 媒体处理类型
 */
export type MediaProcessingType = 
  | 'convert' // 格式转换
  | 'compress' // 压缩
  | 'trim' // 剪辑
  | 'extract' // 提取（如从视频提取音频）
  | 'merge' // 合并
  | 'watermark' // 添加水印
  | 'resize' // 调整尺寸
  | 'rotate' // 旋转
  | 'filter' // 滤镜效果

/**
 * 处理选项基类
 */
export interface BaseProcessingOptions {
  quality?: 'low' | 'medium' | 'high' | 'lossless'
  customArgs?: string[] // 自定义 FFmpeg 参数
}

/**
 * 视频转换选项
 */
export interface VideoConvertOptions extends BaseProcessingOptions {
  format: VideoFormat
  resolution?: string // 如 '1920x1080'
  bitrate?: string // 如 '2M'
  fps?: number // 帧率
  codec?: string // 编码器
}

/**
 * 音频转换选项
 */
export interface AudioConvertOptions extends BaseProcessingOptions {
  format: AudioFormat
  bitrate?: string // 如 '128k'
  sampleRate?: string // 如 '44100'
  channels?: number // 声道数
  codec?: string // 编码器
}

/**
 * 视频剪辑选项
 */
export interface VideoTrimOptions extends BaseProcessingOptions {
  startTime: string // 开始时间，如 '00:01:30'
  endTime?: string // 结束时间
  duration?: string // 持续时间
}

/**
 * 视频压缩选项
 */
export interface VideoCompressOptions extends BaseProcessingOptions {
  targetSize?: number // 目标文件大小（MB）
  compressionLevel?: number // 压缩级别 1-10
  resolution?: string
  bitrate?: string
}

/**
 * 音频提取选项
 */
export interface AudioExtractOptions extends BaseProcessingOptions {
  format: AudioFormat
  startTime?: string
  duration?: string
}

/**
 * 水印选项
 */
export interface WatermarkOptions extends BaseProcessingOptions {
  text?: string // 文字水印
  imageUrl?: string // 图片水印
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'center'
  fontSize?: number
  fontColor?: string
  opacity?: number // 0-1
}

/**
 * 视频合并选项
 */
export interface VideoMergeOptions extends BaseProcessingOptions {
  files: File[]
  transition?: 'none' | 'fade' | 'slide'
  transitionDuration?: number // 转场时长（秒）
}

/**
 * 处理选项联合类型
 */
export type ProcessingOptions = 
  | VideoConvertOptions
  | AudioConvertOptions
  | VideoTrimOptions
  | VideoCompressOptions
  | AudioExtractOptions
  | WatermarkOptions
  | VideoMergeOptions

/**
 * 媒体处理结果
 */
export interface MediaProcessingResult {
  success: boolean
  data?: Uint8Array
  filename?: string
  mimeType?: string
  size?: number
  duration?: number
  error?: string
  processingTime?: number // 处理耗时（毫秒）
}

/**
 * 批量处理任务
 */
export interface BatchProcessingTask {
  id: string
  tasks: MediaProcessingTask[]
  status: ProcessingStatus
  progress: number // 整体进度 0-1
  completedCount: number
  totalCount: number
  createdAt: Date
  completedAt?: Date
}

/**
 * 预设配置
 */
export interface ProcessingPreset {
  id: string
  name: string
  description: string
  type: MediaProcessingType
  mediaType: MediaType
  options: ProcessingOptions
  isCustom?: boolean
}

/**
 * 常用预设
 */
export const DEFAULT_PRESETS: ProcessingPreset[] = [
  {
    id: 'video-to-mp4-hd',
    name: '转换为 MP4 (高清)',
    description: '将视频转换为 MP4 格式，保持高清质量',
    type: 'convert',
    mediaType: 'video',
    options: {
      format: 'mp4',
      quality: 'high',
      resolution: '1920x1080'
    }
  },
  {
    id: 'video-compress-medium',
    name: '视频压缩 (中等)',
    description: '压缩视频文件大小，平衡质量和文件大小',
    type: 'compress',
    mediaType: 'video',
    options: {
      quality: 'medium',
      compressionLevel: 5
    }
  },
  {
    id: 'audio-to-mp3',
    name: '转换为 MP3',
    description: '将音频转换为 MP3 格式',
    type: 'convert',
    mediaType: 'audio',
    options: {
      format: 'mp3',
      bitrate: '128k',
      quality: 'medium'
    }
  },
  {
    id: 'extract-audio-mp3',
    name: '提取音频 (MP3)',
    description: '从视频中提取音频并保存为 MP3',
    type: 'extract',
    mediaType: 'video',
    options: {
      format: 'mp3',
      bitrate: '128k'
    }
  }
]

/**
 * 文件大小格式化
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 时间格式化（秒转为 HH:MM:SS）
 */
export function formatDuration(seconds: number): string {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  
  if (hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }
  
  return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

/**
 * 获取文件的媒体类型
 */
export function getMediaType(file: File): MediaType {
  const type = file.type.toLowerCase()
  
  if (type.startsWith('video/')) {
    return 'video'
  } else if (type.startsWith('audio/')) {
    return 'audio'
  } else if (type.startsWith('image/')) {
    return 'image'
  }
  
  // 根据文件扩展名判断
  const extension = file.name.split('.').pop()?.toLowerCase()
  
  const videoExtensions = ['mp4', 'webm', 'avi', 'mov', 'mkv', 'flv', 'wmv']
  const audioExtensions = ['mp3', 'wav', 'aac', 'ogg', 'flac', 'm4a']
  const imageExtensions = ['jpg', 'jpeg', 'png', 'webp', 'gif', 'bmp']
  
  if (extension && videoExtensions.includes(extension)) {
    return 'video'
  } else if (extension && audioExtensions.includes(extension)) {
    return 'audio'
  } else if (extension && imageExtensions.includes(extension)) {
    return 'image'
  }
  
  return 'video' // 默认为视频
}

/**
 * 获取推荐的输出格式
 */
export function getRecommendedFormats(mediaType: MediaType): string[] {
  switch (mediaType) {
    case 'video':
      return ['mp4', 'webm', 'avi']
    case 'audio':
      return ['mp3', 'wav', 'aac']
    case 'image':
      return ['jpg', 'png', 'webp']
    default:
      return []
  }
}
