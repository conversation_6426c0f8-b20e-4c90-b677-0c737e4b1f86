/**
 * 影视解说相关的类型定义
 */

/**
 * 解说模式类型
 */
export type NarrationMode = 'movie' | 'short-drama' | 'tv-series' | 'documentary'

/**
 * 解说模式配置
 */
export interface NarrationModeConfig {
  id: NarrationMode
  name: string
  description: string
  icon: string
  supportMultipleFiles: boolean
  supportMerge: boolean
}

/**
 * 视频文件信息
 */
export interface VideoFileInfo {
  id: string
  file: File
  name: string
  size: number
  duration?: number
  order: number
  thumbnail?: string
  status: 'pending' | 'processing' | 'completed' | 'error'
  error?: string
}

/**
 * 处理状态
 */
export interface ProcessingState {
  isLoading: boolean
  isProcessing: boolean
  progress: number
  currentStep: string
  error?: string
}

/**
 * 音频提取结果
 */
export interface AudioExtractionResult {
  audioData: Uint8Array
  filename: string
  duration: number
  size: number
}

/**
 * 视频合并结果
 */
export interface VideoMergeResult {
  videoData: Uint8Array
  filename: string
  duration: number
  size: number
}

/**
 * 解说模式配置常量
 */
export const NARRATION_MODES: NarrationModeConfig[] = [
  {
    id: 'movie',
    name: '电影解说',
    description: '单视频上传，适合完整电影的解说制作',
    icon: 'video',
    supportMultipleFiles: false,
    supportMerge: false
  },
  {
    id: 'short-drama',
    name: '短剧解说',
    description: '多视频上传，支持排序和合并',
    icon: 'layers',
    supportMultipleFiles: true,
    supportMerge: true
  },
  {
    id: 'tv-series',
    name: '电视剧解说',
    description: '单视频上传，适合电视剧集的解说',
    icon: 'video',
    supportMultipleFiles: false,
    supportMerge: false
  },
  {
    id: 'documentary',
    name: '纪录片解说',
    description: '单视频上传，适合纪录片的解说制作',
    icon: 'video',
    supportMultipleFiles: false,
    supportMerge: false
  }
]

/**
 * 获取解说模式配置
 */
export function getNarrationModeConfig(mode: NarrationMode): NarrationModeConfig {
  return NARRATION_MODES.find(m => m.id === mode) || NARRATION_MODES[0]
}

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 格式化时长
 */
export function formatDuration(seconds: number): string {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  
  if (hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }
  
  return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

/**
 * 生成唯一ID
 */
export function generateId(): string {
  return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}
