import json
import logging
import time
from typing import Optional, List, Dict, Any
import requests
import tempfile
import os
from io import BytesIO

from agno.tools import Toolkit
from pydantic import BaseModel, Field


logger = logging.getLogger(__name__)


class ASRSegment(BaseModel):
    """ASR转录片段数据模型"""
    text: str = Field(..., description="转录文本")
    start_time: float = Field(..., description="开始时间(秒)")
    end_time: float = Field(..., description="结束时间(秒)")


class ASRResult(BaseModel):
    """ASR转录结果数据模型"""
    segments: List[ASRSegment] = Field(..., description="转录片段列表")
    full_text: str = Field(..., description="完整转录文本")
    duration: float = Field(..., description="音频总时长(秒)")


class BcutASRService:
    """必剪ASR服务封装类"""
    
    API_BASE_URL = "https://member.bilibili.com/x/bcut/rubick-interface"
    API_REQ_UPLOAD = API_BASE_URL + "/resource/create"
    API_COMMIT_UPLOAD = API_BASE_URL + "/resource/create/complete"
    API_CREATE_TASK = API_BASE_URL + "/task"
    API_QUERY_RESULT = API_BASE_URL + "/task/result"
    
    headers = {
        "User-Agent": "Bilibili/1.0.0 (https://www.bilibili.com)",
        "Content-Type": "application/json",
    }
    
    def __init__(self, need_word_time_stamp: bool = False):
        self.session = requests.Session()
        self.need_word_time_stamp = need_word_time_stamp
        self.task_id: Optional[str] = None
        
        # 上传相关属性
        self.__in_boss_key: Optional[str] = None
        self.__resource_id: Optional[str] = None
        self.__upload_id: Optional[str] = None
        self.__upload_urls: List[str] = []
        self.__per_size: Optional[int] = None
        self.__clips: Optional[int] = None
        self.__etags: List[str] = []
        self.__download_url: Optional[str] = None
    
    def transcribe_audio(self, audio_data: bytes) -> ASRResult:
        """转录音频数据"""
        try:
            # 上传音频
            self._upload_audio(audio_data)
            
            # 创建转录任务
            self._create_task()
            
            # 等待转录完成
            result_data = self._wait_for_completion()
            
            # 解析结果
            return self._parse_result(result_data)
            
        except Exception as e:
            logger.error(f"ASR转录失败: {e}")
            raise
    
    def _upload_audio(self, audio_data: bytes) -> None:
        """上传音频数据"""
        payload = json.dumps({
            "type": 2,
            "name": "audio.mp3",
            "size": len(audio_data),
            "ResourceFileType": "mp3",
            "model_id": "8",
        })
        
        resp = requests.post(self.API_REQ_UPLOAD, data=payload, headers=self.headers)
        resp.raise_for_status()
        resp_data = resp.json()["data"]
        
        self.__in_boss_key = resp_data["in_boss_key"]
        self.__resource_id = resp_data["resource_id"]
        self.__upload_id = resp_data["upload_id"]
        self.__upload_urls = resp_data["upload_urls"]
        self.__per_size = resp_data["per_size"]
        self.__clips = len(resp_data["upload_urls"])
        
        logger.info(f"申请上传成功, 总计大小{resp_data['size'] // 1024}KB, {self.__clips}分片")
        
        # 分片上传
        self._upload_parts(audio_data)
        
        # 提交上传
        self._commit_upload()
    
    def _upload_parts(self, audio_data: bytes) -> None:
        """分片上传音频数据"""
        for clip in range(self.__clips):
            start_range = clip * self.__per_size
            end_range = (clip + 1) * self.__per_size
            
            resp = requests.put(
                self.__upload_urls[clip],
                data=audio_data[start_range:end_range],
                headers=self.headers,
            )
            resp.raise_for_status()
            etag = resp.headers.get("Etag")
            self.__etags.append(etag)
            logger.info(f"分片{clip}上传成功")
    
    def _commit_upload(self) -> None:
        """提交上传"""
        data = json.dumps({
            "InBossKey": self.__in_boss_key,
            "ResourceId": self.__resource_id,
            "Etags": ",".join(self.__etags),
            "UploadId": self.__upload_id,
            "model_id": "8",
        })
        
        resp = requests.post(self.API_COMMIT_UPLOAD, data=data, headers=self.headers)
        resp.raise_for_status()
        resp_data = resp.json()
        self.__download_url = resp_data["data"]["download_url"]
        logger.info("提交上传成功")
    
    def _create_task(self) -> str:
        """创建转录任务"""
        resp = requests.post(
            self.API_CREATE_TASK,
            json={"resource": self.__download_url, "model_id": "8"},
            headers=self.headers,
        )
        resp.raise_for_status()
        resp_data = resp.json()
        self.task_id = resp_data["data"]["task_id"]
        logger.info(f"转录任务已创建: {self.task_id}")
        return self.task_id
    
    def _wait_for_completion(self) -> Dict[str, Any]:
        """等待转录完成"""
        for _ in range(500):  # 最多等待500秒
            resp = requests.get(
                self.API_QUERY_RESULT,
                params={"model_id": 7, "task_id": self.task_id},
                headers=self.headers,
            )
            resp.raise_for_status()
            resp_data = resp.json()["data"]
            
            if resp_data["state"] == 4:  # 转录完成
                logger.info("转录完成")
                return json.loads(resp_data["result"])
            
            time.sleep(1)
        
        raise TimeoutError("转录任务超时")
    
    def _parse_result(self, result_data: Dict[str, Any]) -> ASRResult:
        """解析转录结果"""
        segments = []
        full_text_parts = []
        
        if self.need_word_time_stamp:
            # 词级别时间戳
            for utterance in result_data["utterances"]:
                for word in utterance["words"]:
                    segments.append(ASRSegment(
                        text=word["label"].strip(),
                        start_time=word["start_time"],
                        end_time=word["end_time"]
                    ))
                    full_text_parts.append(word["label"].strip())
        else:
            # 句子级别时间戳
            for utterance in result_data["utterances"]:
                segments.append(ASRSegment(
                    text=utterance["transcript"],
                    start_time=utterance["start_time"],
                    end_time=utterance["end_time"]
                ))
                full_text_parts.append(utterance["transcript"])
        
        full_text = " ".join(full_text_parts)
        duration = segments[-1].end_time if segments else 0.0
        
        return ASRResult(
            segments=segments,
            full_text=full_text,
            duration=duration
        )


class ASRTools(Toolkit):
    """ASR音频转录工具集"""
    
    def __init__(self, need_word_time_stamp: bool = False):
        super().__init__(name="asr_tools")
        self.need_word_time_stamp = need_word_time_stamp
        self.asr_service = BcutASRService(need_word_time_stamp=need_word_time_stamp)
        
        # 注册工具函数
        self.register(self.transcribe_audio_file)
        self.register(self.transcribe_audio_url)
        self.register(self.generate_srt_subtitle)
    
    def transcribe_audio_file(self, file_path: str) -> str:
        """
        转录本地音频文件
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            转录结果的JSON字符串，包含完整文本和时间戳信息
        """
        try:
            with open(file_path, 'rb') as f:
                audio_data = f.read()
            
            result = self.asr_service.transcribe_audio(audio_data)
            return json.dumps(result.dict(), ensure_ascii=False, indent=2)
            
        except Exception as e:
            return f"转录失败: {str(e)}"
    
    def transcribe_audio_url(self, audio_url: str) -> str:
        """
        转录网络音频文件
        
        Args:
            audio_url: 音频文件URL
            
        Returns:
            转录结果的JSON字符串，包含完整文本和时间戳信息
        """
        try:
            # 下载音频文件
            resp = requests.get(audio_url)
            resp.raise_for_status()
            audio_data = resp.content
            
            result = self.asr_service.transcribe_audio(audio_data)
            return json.dumps(result.dict(), ensure_ascii=False, indent=2)
            
        except Exception as e:
            return f"转录失败: {str(e)}"
    
    def generate_srt_subtitle(self, transcription_result: str) -> str:
        """
        根据转录结果生成SRT字幕文件内容
        
        Args:
            transcription_result: transcribe_audio_file或transcribe_audio_url的返回结果
            
        Returns:
            SRT格式的字幕内容
        """
        try:
            # 解析转录结果
            result_data = json.loads(transcription_result)
            segments = result_data["segments"]
            
            srt_content = []
            for i, segment in enumerate(segments, 1):
                start_time = self._seconds_to_srt_time(segment["start_time"])
                end_time = self._seconds_to_srt_time(segment["end_time"])
                text = segment["text"]
                
                srt_content.append(f"{i}")
                srt_content.append(f"{start_time} --> {end_time}")
                srt_content.append(text)
                srt_content.append("")  # 空行分隔
            
            return "\n".join(srt_content)
            
        except Exception as e:
            return f"生成SRT字幕失败: {str(e)}"
    
    def _seconds_to_srt_time(self, seconds: float) -> str:
        """将秒数转换为SRT时间格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millisecs = int((seconds % 1) * 1000)
        
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"
